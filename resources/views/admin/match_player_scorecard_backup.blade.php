<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2a5298">
    <title>Cricket Scorecard</title>

    <style>
        /* Mobile-First Design for iOS/Android WebView */
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .scorecard-container {
            max-width: 100%;
            margin: 0;
            background: white;
            min-height: 100vh;
        }

        .match-header {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            padding: 20px 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .match-header h2 {
            margin: 0 0 8px 0;
            font-size: 1.3em;
            font-weight: 600;
        }

        .match-header p {
            margin: 4px 0;
            opacity: 0.9;
            font-size: 0.9em;
        }

        /* Mobile-Optimized Team Summary */
        .teams-summary {
            padding: 15px;
            background: #f8f9fa;
        }

        .team-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 16px;
            margin: 8px 0;
            background: white;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            -webkit-user-select: none;
            user-select: none;
            position: relative;
        }

        .team-summary:active {
            transform: scale(0.98);
            background: #f8f9fa;
        }

        .team-summary.active {
            background: #e3f2fd;
            border-color: #2196f3;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
        }

        .team-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .team-name {
            font-weight: 600;
            font-size: 1.1em;
            color: #2c3e50;
            margin-right: 12px;
        }

        .batting-indicator {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.75em;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .team-score-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .team-score {
            font-size: 1.3em;
            font-weight: 700;
            color: #2c3e50;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .toggle-icon {
            font-size: 1.4em;
            color: #6c757d;
            transition: transform 0.3s ease;
            padding: 4px;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        /* Mobile-Optimized Collapsible Content */
        .team-details {
            display: none;
            padding: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            animation: slideDown 0.3s ease-out;
        }

        .team-details.show {
            display: block;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Mobile-First Table Styles */
        .table-container {
            padding: 15px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .batting-table, .bowling-table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
            font-size: 0.9em;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .batting-table th, .bowling-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-size: 0.85em;
            font-weight: 600;
            border: none;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .batting-table td, .bowling-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #f1f3f4;
            font-size: 0.9em;
            vertical-align: middle;
        }

        .batting-table tr:last-child td,
        .bowling-table tr:last-child td {
            border-bottom: none;
        }

        .batting-table tr:nth-child(even),
        .bowling-table tr:nth-child(even) {
            background: #fafbfc;
        }

        .player-name {
            text-align: left !important;
            font-weight: 500;
            min-width: 120px;
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .player-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.8em;
            flex-shrink: 0;
            text-transform: uppercase;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid white;
        }

        /* Role-based avatar colors */
        .player-avatar.batter {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .player-avatar.bowler {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .player-avatar.all-rounder {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .player-avatar.wicketkeeper {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        /* Hidden refresh status (for debugging only) */
        .refresh-status-hidden {
            display: none !important;
        }

        /* Zero visual disruption - no opacity changes */
        .team-details, .batting-table, .bowling-table {
            /* No transitions to avoid any visual flicker */
        }

        /* Loading indicator completely hidden - zero visual disruption */
        .loading-indicator {
            display: none !important;
        }

        .player-details {
            flex: 1;
            min-width: 0;
        }

        .player-name div:first-child {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .player-role {
            font-size: 0.75em;
            color: #6c757d;
            font-style: italic;
        }

        .player-role.not-out {
            color: #28a745;
            font-weight: 600;
        }

        .player-role.batting {
            color: #007bff;
            font-weight: 600;
            /* No animation to avoid any visual disruption */
        }

        .extras-row {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e) !important;
            font-weight: 600;
        }

        .total-row {
            background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
            color: white;
            font-weight: 700;
        }

        .strike-rate {
            font-weight: 600;
            color: #00b894;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .economy-rate {
            font-weight: 600;
            color: #e17055;
            font-family: 'SF Mono', Monaco, monospace;
        }

        /* Section Headers */
        .section-header {
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: 700;
            margin: 25px 15px 15px 15px;
            padding: 8px 0;
            border-bottom: 3px solid #74b9ff;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Compressed responsive rules */
        @media (max-width: 480px) {
            .match-header h2{font-size:1.1em}.team-name{font-size:1em}.team-score{font-size:1.2em}
            .batting-table th,.bowling-table th{font-size:0.75em;padding:10px 6px}
            .batting-table td,.bowling-table td{font-size:0.85em;padding:10px 6px}
            .section-header{font-size:1em;margin:20px 10px 10px 10px}.table-container{padding:10px}
            .player-avatar{width:30px;height:30px;font-size:0.7em}
            .player-info{gap:8px}
        }
    </style>
</head>
<body>

<div class="scorecard-container">
    <div class="match-header">
        <h2>{{ $matchInfo['team_home']['name'] ?? 'Team 1' }} vs {{ $matchInfo['team_away']['name'] ?? 'Team 2' }}</h2>
        <p class="mb-1">{{ $format }} • Game ID: {{ $gameId }}</p>
        <p class="mb-0"><small>{{ $matchInfo['venue'] ?? 'Venue TBD' }}</small></p>
        <p>Match Status : <span>{{ $matchInfo['display_status'] }}</span></p>
        <p class="mb-0"><small>Score stats at match level</small></p>

        <!-- Hidden auto-refresh status (only for development/debugging) -->
        <div class="refresh-status-hidden" style="display: none;">
            <div id="refresh-timer">Next update in <span id="refresh-counter">30</span>s</div>
            <div id="refresh-status">Auto-refresh active</div>
            <div>Last updated: <span id="last-updated-time">{{ date('H:i:s') }}</span></div>
        </div>

        <!-- Subtle loading indicator -->
        <div id="loading-indicator" class="loading-indicator" style="display: none;">
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
        </div>
    </div>

    <!-- Teams Summary Section -->
    <div class="teams-summary">
        @foreach($teamWisePlayers as $index => $teamData)
            @php
                // Get team score from innings data
                $teamScore = $teamData['score'] ?? null;
                $teamRuns = $teamScore['runs'] ?? 0;
                $teamWickets = $teamScore['wickets'] ?? 0;
                $teamOvers = $teamScore['overs'] ?? '0.0';

                // Determine if this team is currently batting (you can adjust this logic)
                $isBatting = $index == 0; // For now, assume first team is batting
            @endphp

            <div class="team-summary" onclick="toggleTeam('team-{{ $index }}')" ontouchstart="">
                <div class="team-info">
                    <span class="team-name">{{ $teamData['team_name'] }}</span>
                    @if($isBatting)
                        <span class="batting-indicator">Batting</span>
                    @endif
                </div>
                <div class="team-score-section">
                    <span class="team-score">{{ $teamRuns }}/{{ $teamWickets }} ({{ $teamOvers }} ov)</span>
                    <span class="toggle-icon" id="icon-team-{{ $index }}">▼</span>
                </div>
            </div>

            <div class="team-details" id="team-{{ $index }}">
                @php
                    $battingPlayers = collect($teamData['players'])->filter(function($player) {
                        return $player['balls'] > 0;
                    })->sortBy('batting_position');

                    $bowlingPlayers = collect($teamData['players'])->filter(function($player) {
                        return $player['wickets'] > 0 || $player['overs'] > 0;
                    })->sortByDesc('wickets');

                    $yetToBat = collect($teamData['players'])->filter(function($player) {
                        return $player['balls'] == 0 && $player['playing_xi'] == true;
                    });

                    $extras = $teamScore['extras'] ?? [];
                    $totalExtras = ($extras['byes'] ?? 0) + ($extras['legbyes'] ?? 0) + ($extras['wides'] ?? 0) + ($extras['noballs'] ?? 0) + ($extras['penalty'] ?? 0);
                @endphp
                <!-- Batting Section -->
                @if($battingPlayers->count() > 0)
                <div class="section-header">Batting</div>
                <div class="table-container">
                    <table class="batting-table">
                <thead>
                    <tr>
                        <th style="width: 35%;">Batter</th>
                        <th>R</th>
                        <th>B</th>
                        <th>4s</th>
                        <th>6s</th>
                        <th>S/R</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($battingPlayers as $player)
                    <tr>
                        <td class="player-name">
                            <div class="player-info">
                                <div class="player-avatar {{ $player['role'] }}">
                                    {{ strtoupper(substr($player['player_name'], 0, 1)) }}{{ strtoupper(substr(explode(' ', $player['player_name'])[1] ?? '', 0, 1)) }}
                                </div>
                                <div class="player-details">
                                    <div>{{ $player['player_name'] }}</div>
                                    <div class="player-role">
                                        @if($player['dismissal'] !== 'not out' && $player['balls'] > 0)
                                            @if(!empty($player['howout_short']))
                                                {{ $player['howout_short'] }}
                                            @elseif(!empty($player['howout']))
                                                {{ $player['howout'] }}
                                            @else
                                                {{ $player['dismissal'] }}
                                            @endif
                                        @elseif($player['balls'] > 0)
                                            @if($matchInfo['status'] === 'Match Ended')
                                                <span class="not-out">Not out</span>
                                            @else
                                                <span class="batting">Batting</span>
                                            @endif
                                        @else
                                            {{ ucfirst($player['role']) }}
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>{{ $player['runs'] }}</td>
                        <td>{{ $player['balls'] }}</td>
                        <td>{{ $player['fours'] }}</td>
                        <td>{{ $player['sixes'] }}</td>
                        <td class="strike-rate">{{ is_numeric($player['strike_rate']) ? number_format((float)$player['strike_rate'], 2) : $player['strike_rate'] }}</td>
                    </tr>
                    @endforeach

                    <tr class="extras-row">
                        <td class="player-name">EXTRAS<br><small>(nb {{ $extras['noballs'] ?? 0 }}, wd {{ $extras['wides'] ?? 0 }}, b {{ $extras['byes'] ?? 0 }}, lb {{ $extras['legbyes'] ?? 0 }}, pen {{ $extras['penalty'] ?? 0 }})</small></td>
                        <td>{{ $totalExtras }}</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>

                    <tr class="total-row">
                        <td class="player-name">Total<br><small>({{ $teamWickets }} wickets, {{ $teamOvers }} overs)</small></td>
                        <td>{{ $teamRuns }}</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
                    </table>
                </div>
            @endif

                <!-- Yet to Bat Section -->
                @if($yetToBat->count() > 0)
                <div class="section-header">Yet to Bat</div>
                <div class="table-container">
                    <table class="batting-table">
                <thead>
                    <tr>
                        <th style="width: 25%;">Batter</th>
                        <th>R</th>
                        <th>B</th>
                        <th>4s</th>
                        <th>6s</th>
                        <th>S/R</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($yetToBat as $player)
                    <tr>
                        <td class="player-name">
                            <div class="player-info">
                                <div class="player-avatar {{ $player['role'] }}">
                                    {{ strtoupper(substr($player['player_name'], 0, 1)) }}{{ strtoupper(substr(explode(' ', $player['player_name'])[1] ?? '', 0, 1)) }}
                                </div>
                                <div class="player-details">
                                    <div>{{ $player['player_name'] }}</div>
                                    <div class="player-role">{{ ucfirst($player['role']) }}</div>
                                </div>
                            </div>
                        </td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
                    @endforeach
                </tbody>
                    </table>
                </div>
            @endif

                <!-- Bowling Section -->
                @if($bowlingPlayers->count() > 0)
                <div class="section-header">Bowling</div>
                <div class="table-container">
                    <table class="bowling-table">
                <thead>
                    <tr>
                        <th style="width: 25%;">Bowler</th>
                        <th>O</th>
                        <th>M</th>
                        <th>R</th>
                        <th>W</th>
                        <th>Eco</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($bowlingPlayers as $player)
                    <tr>
                        <td class="player-name">
                            <div class="player-info">
                                <div class="player-avatar {{ $player['role'] }}">
                                    {{ strtoupper(substr($player['player_name'], 0, 1)) }}{{ strtoupper(substr(explode(' ', $player['player_name'])[1] ?? '', 0, 1)) }}
                                </div>
                                <div class="player-details">
                                    <div>{{ $player['player_name'] }}</div>
                                    <div class="player-role">{{ ucfirst($player['role']) }}</div>
                                </div>
                            </div>
                        </td>
                        <td>{{ $player['overs'] ?: '0' }}</td>
                        <td>{{ $player['maidens'] }}</td>
                        <td>{{ $player['runs_conceded'] }}</td>
                        <td>{{ $player['wickets'] }}</td>
                        <td class="economy-rate">{{ is_numeric($player['economy']) ? number_format((float)$player['economy'], 2) : ($player['economy'] ?: '0.00') }}</td>
                    </tr>
                    @endforeach
                </tbody>
                    </table>
                </div>
                @endif
            </div>
        @endforeach
    </div>
</div>

<script>
// Mobile-optimized JavaScript for iOS/Android WebView
function toggleTeam(teamId) {
    const teamDetails = document.getElementById(teamId);
    const icon = document.getElementById('icon-' + teamId);
    const teamSummary = teamDetails.previousElementSibling;

    // Add haptic feedback for iOS
    if (window.navigator && window.navigator.vibrate) {
        window.navigator.vibrate(50);
    }

    if (teamDetails.classList.contains('show')) {
        // Close current team
        teamDetails.classList.remove('show');
        icon.classList.remove('rotated');
        icon.textContent = '▼';
        teamSummary.classList.remove('active');
    } else {
        // Close all other teams first
        document.querySelectorAll('.team-details').forEach(detail => {
            detail.classList.remove('show');
        });
        document.querySelectorAll('.toggle-icon').forEach(ic => {
            ic.classList.remove('rotated');
            ic.textContent = '▼';
        });
        document.querySelectorAll('.team-summary').forEach(summary => {
            summary.classList.remove('active');
        });

        // Open clicked team with smooth animation
        setTimeout(() => {
            teamDetails.classList.add('show');
            icon.classList.add('rotated');
            icon.textContent = '▲';
            teamSummary.classList.add('active');
        }, 100);
    }
}

// Prevent zoom on double tap for iOS
document.addEventListener('touchend', function (event) {
    var now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        event.preventDefault();
    }
    lastTouchEnd = now;
}, false);

var lastTouchEnd = 0;

// Silent auto-refresh functionality with AJAX (preserves UI state)
let refreshInterval;
let refreshCounter = 30;
let isRefreshing = false;
let openTeams = new Set(); // Track which teams are expanded

function updateRefreshCounter() {
    // Silent counter update (for debugging only)
    const counterElement = document.getElementById('refresh-counter');
    if (counterElement) {
        counterElement.textContent = refreshCounter;
    }
}

function saveUIState() {
    // Save which teams are currently expanded
    openTeams.clear();
    document.querySelectorAll('.team-details.show').forEach(element => {
        openTeams.add(element.id);
    });
    console.log('Saved UI state:', Array.from(openTeams));
}

function restoreUIState() {
    // Restore expanded teams after data refresh
    openTeams.forEach(teamId => {
        const teamDetails = document.getElementById(teamId);
        const teamSummary = document.querySelector(`[onclick="toggleTeam('${teamId}')"]`);
        const icon = document.getElementById('icon-' + teamId);

        if (teamDetails && teamSummary && icon) {
            teamDetails.classList.add('show');
            teamSummary.classList.add('active');
            icon.classList.add('rotated');
            icon.textContent = '▲';
        }
    });
    console.log('Restored UI state:', Array.from(openTeams));
}

function updateScorecardSmoothly(newContainer, currentContainer) {
    // Update only specific sections to minimize visual disruption
    try {
        // Update team scores
        const newTeamSummaries = newContainer.querySelectorAll('.team-summary');
        const currentTeamSummaries = currentContainer.querySelectorAll('.team-summary');

        newTeamSummaries.forEach((newSummary, index) => {
            if (currentTeamSummaries[index]) {
                const newScore = newSummary.querySelector('.team-score');
                const currentScore = currentTeamSummaries[index].querySelector('.team-score');
                if (newScore && currentScore && newScore.textContent !== currentScore.textContent) {
                    currentScore.textContent = newScore.textContent;
                    console.log('Updated team score');
                }
            }
        });

        // Update player stats in tables
        const newTables = newContainer.querySelectorAll('.batting-table, .bowling-table');
        const currentTables = currentContainer.querySelectorAll('.batting-table, .bowling-table');

        newTables.forEach((newTable, index) => {
            if (currentTables[index]) {
                const newRows = newTable.querySelectorAll('tbody tr');
                const currentRows = currentTables[index].querySelectorAll('tbody tr');

                newRows.forEach((newRow, rowIndex) => {
                    if (currentRows[rowIndex]) {
                        const newCells = newRow.querySelectorAll('td');
                        const currentCells = currentRows[rowIndex].querySelectorAll('td');

                        newCells.forEach((newCell, cellIndex) => {
                            if (currentCells[cellIndex] && newCell.textContent !== currentCells[cellIndex].textContent) {
                                currentCells[cellIndex].textContent = newCell.textContent;
                            }
                        });
                    }
                });
            }
        });

        console.log('Smooth update completed');
    } catch (error) {
        console.log('Smooth update failed, using full replacement:', error);
        currentContainer.innerHTML = newContainer.innerHTML;
    }
}

function startSilentAutoRefresh() {
    // Clear any existing interval
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }

    console.log('Silent auto-refresh started - AJAX updates every 30 seconds');

    refreshInterval = setInterval(() => {
        refreshCounter--;
        updateRefreshCounter();

        if (refreshCounter <= 0) {
            refreshScorecardData();
            refreshCounter = 30; // Reset counter
        }
    }, 1000); // Update every second
}

function refreshScorecardData() {
    if (isRefreshing) return;

    isRefreshing = true;
    console.log('Refreshing scorecard data via AJAX...');

    // Save current UI state (which teams are open)
    saveUIState();

    // No visual loading indicator to avoid any screen disruption
    // Loading happens silently in background

    // Update status silently
    const refreshStatus = document.getElementById('refresh-status');
    if (refreshStatus) {
        refreshStatus.textContent = 'Updating...';
    }

    // Get current URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const gameId = urlParams.get('game_id');
    const token = urlParams.get('token');

    // Make AJAX call to get fresh data
    fetch(`${window.location.pathname}?game_id=${gameId}&token=${token}&response=`)
        .then(response => response.text())
        .then(html => {
            // Create temporary container to parse new HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // Extract the scorecard container from new HTML
            const newScorecardContainer = tempDiv.querySelector('.scorecard-container');
            const currentScorecardContainer = document.querySelector('.scorecard-container');

            if (newScorecardContainer && currentScorecardContainer) {
                // Preserve the current container's position
                const scrollPosition = window.scrollY;

                // Instant update with zero visual disruption
                updateScorecardSmoothly(newScorecardContainer, currentScorecardContainer);

                // Restore scroll position immediately
                window.scrollTo(0, scrollPosition);

                // No loading indicator to hide - completely silent refresh

                // Restore UI state immediately
                restoreUIState();
                isRefreshing = false;
                console.log('Scorecard refresh completed with zero visual disruption');

                // Update last updated time
                const lastUpdatedElement = document.getElementById('last-updated-time');
                if (lastUpdatedElement) {
                    const now = new Date();
                    lastUpdatedElement.textContent = now.toLocaleTimeString();
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing scorecard:', error);
            isRefreshing = false;

            // Fallback to page reload if AJAX fails (silent)
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
}

// Optimize for mobile performance and start silent auto-refresh
document.addEventListener('DOMContentLoaded', function() {
    // Add touch event listeners for better mobile interaction
    const teamSummaries = document.querySelectorAll('.team-summary');
    teamSummaries.forEach(summary => {
        summary.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        });

        summary.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });

        summary.addEventListener('touchcancel', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Start silent auto-refresh (no visible controls)
    startSilentAutoRefresh();
    updateRefreshCounter();

    console.log('Cricket scorecard loaded with silent 30-second auto-refresh');
});
</script>

</body>
</html>
