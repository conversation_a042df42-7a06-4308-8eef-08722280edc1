<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Player Points</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">

    <style>
        body, html {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        .full-page-table {
            height: 100vh;
            overflow: auto;
            padding: 20px;
        }
    </style>
</head>
<body>

<div class="full-page-table bg-light">
    <h1>Match Player Points (Game ID: {{ $gameId }})</h1>
    <h4>Format: {{ $format }}</h4>
    <p><strong>Match:</strong> {{ $matchInfo['team_home']['name'] }} vs {{ $matchInfo['team_away']['name'] }}</p>
    <p><strong>Venue:</strong> {{ $matchInfo['venue'] }}</p>
    <p><strong>Total Players:</strong> {{ count($matchPlayerPoints) }}</p>

    <div class="table-responsive mt-4">
        <table id="playerPointsTable" class="table table-striped table-bordered">
            <thead class="table-dark">
                <tr>
                    <th>Player Name</th>
                    <th>Player Key</th>
                    <th>Role</th>
                    <th>Batting Position</th>
                    <th>Team ID</th>
                    <th>Inning</th>
                    <th>Playing XI</th>
                    <th>Starting XI Points</th>
                    <th>Runs</th>
                    <th>Balls</th>
                    <th>Fours</th>
                    <th>Sixes</th>
                    <th>Strike Rate</th>
                    <th>Wickets</th>
                    <th>Overs</th>
                    <th>Balls Bowled</th>
                    <th>Runs Conceded</th>
                    <th>Economy</th>
                    <th>Dots</th>
                    <th>Catches</th>
                    <th>Run Outs</th>
                    <th>Stumped</th>
                    <th>LBW Bonus</th>
                    <th>Total Points</th>
                </tr>
            </thead>
            <tbody>
                @foreach($matchPlayerPoints as $player)
                <tr>
                    <td>{{ $player['player_name'] }}</td>
                    <td>{{ $player['player_key'] }}</td>
                    <td>{{ ucfirst($player['player_role']) }}</td>
                    <td>{{ $player['batting_position'] }}</td>
                    <td>{{ $player['team_id'] }}</td>
                    <td>{{ $player['inning_number'] }}</td>
                    
                    <td>{{ !empty($player['playing_xi']) ? 'Yes' : 'No' }}</td>
                    <td>{{ $player['start_point'] }}</td>
                    <td>{{ $player['run'] }}</td>
                    <td>{{ $player['balls'] }}</td>
                    <td>{{ $player['fours_count'] }}</td>
                    <td>{{ $player['sixs_count'] }}</td>
                    <td>{{ $player['strike_rate_per'] }}</td>
                    <td>{{ $player['wickets'] }}</td>
                    <td>{{ $player['overs'] }}</td>
                    <td>{{ $player['balls_bowled'] }}</td>
                    <td>{{ $player['bowler_runs_conceded'] }}</td>
                    <td>{{ $player['economy_rate_per'] }}</td>
                    <td>{{ $player['dots'] }}</td>
                    <td>{{ $player['catch_count'] }}</td>
                    <td>{{ $player['run_outs_count'] }}</td>
                    <td>{{ $player['stumped_count'] }}</td>
                    <td>{{ $player['lbw_bowled_count'] }}</td>
                    <td class="fw-bold">{{ $player['total_bonus'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- jQuery + DataTables JS + Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
$(document).ready(function() {
    $('#playerPointsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[20, 'desc']]
    });
});
</script>

</body>
</html>
