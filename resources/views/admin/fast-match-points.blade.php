<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fast Match Points API Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .response { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
        .stat-card { background: #e9ecef; padding: 15px; border-radius: 4px; }
        .loading { display: none; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Fast Match Points API Tester</h1>
        <p>High-performance cricket match points calculator with conditional response filtering</p>

        <form id="apiForm">
            <div class="form-group">
                <label for="gameId">Game ID:</label>
                <input type="text" id="gameId" name="game_id" placeholder="Enter match game_id" required>
            </div>

            <div class="form-group">
                <label for="type">Response Type:</label>
                <select id="type" name="type">
                    <option value="all">All (Points + Counts)</option>
                    <option value="points">Points Only</option>
                    <option value="count">Counts Only</option>
                </select>
            </div>

            <div class="form-group">
                <label for="token">API Token:</label>
                <input type="text" id="token" name="token" value="ea9fc1ae0ce14b1394f7589bb2437878" required>
            </div>

            <button type="submit">🔥 Calculate Points</button>
            <button type="button" onclick="getFormats()">📋 Get Formats</button>
            <button type="button" onclick="clearCache()">🗑️ Clear Cache</button>
            
            <div class="loading" id="loading">⏳ Processing...</div>
        </form>

        <div id="response" class="response" style="display: none;">
            <h3>API Response:</h3>
            <pre id="responseContent"></pre>
        </div>

        <div id="stats" class="stats" style="display: none;">
            <div class="stat-card">
                <h4>📊 Response Stats</h4>
                <div id="responseStats"></div>
            </div>
            <div class="stat-card">
                <h4>⚡ Performance</h4>
                <div id="performanceStats"></div>
            </div>
        </div>
    </div>

    <script>
        const apiForm = document.getElementById('apiForm');
        const loading = document.getElementById('loading');
        const response = document.getElementById('response');
        const responseContent = document.getElementById('responseContent');
        const stats = document.getElementById('stats');
        const responseStats = document.getElementById('responseStats');
        const performanceStats = document.getElementById('performanceStats');

        apiForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(apiForm);
            const params = new URLSearchParams(formData);
            
            showLoading(true);
            hideResponse();
            
            const startTime = performance.now();
            
            try {
                const apiResponse = await fetch(`/api/v1/match-points/calculate?${params}`);
                const endTime = performance.now();
                const data = await apiResponse.json();
                
                showResponse(data, apiResponse.status === 200);
                showStats(data, endTime - startTime, apiResponse.headers);
                
            } catch (error) {
                showResponse({ error: error.message }, false);
            } finally {
                showLoading(false);
            }
        });

        async function getFormats() {
            showLoading(true);
            try {
                const apiResponse = await fetch('/api/v1/match-points/formats');
                const data = await apiResponse.json();
                showResponse(data, apiResponse.status === 200);
            } catch (error) {
                showResponse({ error: error.message }, false);
            } finally {
                showLoading(false);
            }
        }

        async function clearCache() {
            const gameId = document.getElementById('gameId').value;
            const token = document.getElementById('token').value;
            
            if (!gameId) {
                alert('Please enter a Game ID first');
                return;
            }
            
            showLoading(true);
            try {
                const apiResponse = await fetch(`/api/v1/match-points/cache?game_id=${gameId}&token=${token}`, {
                    method: 'DELETE'
                });
                const data = await apiResponse.json();
                showResponse(data, apiResponse.status === 200);
            } catch (error) {
                showResponse({ error: error.message }, false);
            } finally {
                showLoading(false);
            }
        }

        function showLoading(show) {
            loading.style.display = show ? 'block' : 'none';
        }

        function hideResponse() {
            response.style.display = 'none';
            stats.style.display = 'none';
        }

        function showResponse(data, isSuccess) {
            response.className = `response ${isSuccess ? 'success' : 'error'}`;
            response.style.display = 'block';
            responseContent.textContent = JSON.stringify(data, null, 2);
        }

        function showStats(data, responseTime, headers) {
            if (data.status === 'success') {
                stats.style.display = 'block';
                
                responseStats.innerHTML = `
                    <p><strong>Type:</strong> ${data.response_type || 'N/A'}</p>
                    <p><strong>Format:</strong> ${data.format || 'N/A'}</p>
                    <p><strong>Players:</strong> ${data.total_players || 0}</p>
                    <p><strong>Game ID:</strong> ${data.game_id || 'N/A'}</p>
                `;
                
                performanceStats.innerHTML = `
                    <p><strong>Response Time:</strong> ${responseTime.toFixed(2)}ms</p>
                    <p><strong>Cache Control:</strong> ${headers.get('Cache-Control') || 'None'}</p>
                    <p><strong>Response Type:</strong> ${headers.get('X-Response-Type') || 'N/A'}</p>
                    <p><strong>Calculated:</strong> ${data.calculated_at || 'N/A'}</p>
                `;
            }
        }

        // Auto-fill game ID for testing
        document.addEventListener('DOMContentLoaded', () => {
            const gameIdInput = document.getElementById('gameId');
            if (!gameIdInput.value) {
                gameIdInput.placeholder = 'e.g., 264117';
            }
        });
    </script>
</body>
</html>
