# Sports Interactive Match Points Calculator

This controller calculates **fantasy cricket points** for each player in a match using data fetched from the **Sportz.io** scorecard API.

## 📌 Overview

- API Endpoint:  
`GET /get-sport-interactive-scorecard?game_id=<code>&token=<token>&lang=en&feed_format=json`

- Fetches the complete **scorecard data** for a given `game_id`.
- Applies configured **fantasy points rules** based on match format (`T20`, `ODI`, `TEST`, `T10`).
- Returns:
  - **Per-player total points**
  - **Detailed stat-wise breakdown** (batting, bowling, fielding, bonus points)
  - **Match Info**

---

## 🏏 Scoring Configuration

### Normal Points (per format)

| Event        | T20 | ODI | TEST | T10 |
|--------------|-----|-----|------|-----|
| Starting XI  | 4   | 4   | 4    | 4   |
| Run          | 1   | 1   | 1    | 1   |
| Wicket       | 25  | 25  | 16   | 25  |
| Catch        | 8   | 8   | 8    | 8   |
| Run Out      | 6   | 6   | 6    | 6   |
| Stumping     | 12  | 12  | 12   | 12  |
| Duck Penalty | -2  | -3  | -4   | -2  |

### Bonus Points

- Boundaries
- Sixes
- Milestone runs (30, 50, 100)
- Maiden overs
- Wickets milestones (2, 3, 4, 5 wickets)
- LBW/Bowled dismissals

### Economy Rate & Strike Rate Bonus/Penalty
- Economy rate bonus/penalty for bowlers based on overs and runs conceded.
- Strike rate bonus/penalty for batters based on runs scored per 100 balls.

---

## 📈 Data Flow

1. **Token Validation**: Validates API token.
2. **Fetch Scorecard**: Calls Sportz.io API to get the complete match data.
3. **Points Calculation**:
   - **Starting XI points**
   - Process:
     - `Batting`: Runs, Boundaries, Milestones, Strike Rate, Duck
     - `Bowling`: Wickets, Economy Rate, Maidens, Wicket bonuses
     - `Fielding`: Catches, Stumping, Run Outs
     - **LBW/Bowled Bonus**
4. **Response**:
   - `match_details`
   - `points`: Player-wise total and breakup
   - `match_player_points`: Flattened details for display/UI

---

## ⚙️ Key Methods

| Method                | Description                    |
|-----------------------|--------------------------------|
| `calculate()`         | Main entry to compute points   |
| `fetchScorecard()`    | Fetches data from Sportz.io API|
| `processBatting()`    | Batting stats calculation      |
| `processBowling()`    | Bowling stats calculation      |
| `processFielding()`   | Fielding stats calculation     |
| `processDismissalBonus()` | Bonus for LBW/Bowled      |
| `addStat()`           | Utility to update totals and breakdown |

---

## 🗂️ Response Structure

### JSON Response (when `response=json`):

```json
{
  "status": "success",
  "calculated": "timestamp",
  "game_id": "123456",
  "format": "T20",
  "match_details": {...},
  "points": [
    {
      "player_id": 1,
      "player_name": "Player Name",
      "role": "batter",
      "team_short": "IND",
      "total_points": 50,
      "breakup": {...}
    }
  ],
  "match_player_points": [...]
}

===================================================================================
1. addStat() Function Purpose
addStat() is a central helper method that:

Adds count of a particular stat (like runs, wickets, catches) for a player.

Calculates total points for that stat (count * per point value).

Adds that to:

$tot — total points for the player.

$brk — detailed breakdown of all stats (category-wise count, per point, total points).

✅ 2. addStat Method Breakdown

private function addStat($pid, $cat, $cnt, $per, &$tot, &$brk)

Param	Meaning
$pid	Player ID
$cat	Category/stat name (like 'runs', 'wickets')
$cnt	The count of the stat (e.g., runs scored)
$per	Points per count (e.g., 1 point per run)
$tot	Total points per player (ref updated)
$brk	Detailed breakdown of stats per player

What it does:
Skips if count is 0 or points per stat is null.

Multiplies $cnt * $per = $pts, rounded to 2 decimals.

Adds the $pts to total player points in $tot.

Updates $brk with:

count of that stat

per_point (how many points per unit stat)

points earned in that category

✅ 3. $cnt and $pts_ Closures
In this code:

php
Copy
Edit
$cnt = static function (array $bk, string $k) {
    return $bk[$k]['count'] ?? 0;
};

$pts_ = static function (array $bk, string $k): int {
    return $bk[$k]['points'] ?? 0;
};
These are helper functions to retrieve data from $breaks for any player:

$cnt($bk, 'runs') → number of runs scored

$pts_($bk, 'runs') → total points earned from runs

This is just to make data access easier when generating player stats for response.

✅ 4. $brk Variable
Stands for "breakdown"

Structure:

php
Copy
Edit
$brk[$playerId][$category] = [
    'count' => number of events,    // like 3 wickets
    'per_point' => points per event,// like 25 per wicket
    'points' => total points earned // 3 wickets * 25 = 75
];
Example for a player who hit 2 sixes:

php
Copy
Edit
[
    'sixes' => [
        'count' => 2,
        'per_point' => 2,
        'points' => 4
    ]
]
✅ Summary
Term	Description
addStat	Adds count, calculates points, updates total and breakdown
$cnt	Fetches count of a specific stat for a player
$pts_	Fetches total points earned for a specific stat
$brk	Full points breakup for a player, category-wise