<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\Cricket\CricketPointsService;

class FieldingPointsTest extends TestCase
{
    /**
     * Test catch points calculation for different formats
     */
    public function test_catch_points_calculation()
    {
        // Test T20 format
        $points = CricketPointsService::calculateCricketBonusPoints('T20', 'catch', 2);
        $this->assertEquals(16, $points); // 2 catches * 8 points each = 16

        // Test ODI format
        $points = CricketPointsService::calculateCricketBonusPoints('ODI', 'catch', 1);
        $this->assertEquals(8, $points); // 1 catch * 8 points = 8

        // Test Test format
        $points = CricketPointsService::calculateCricketBonusPoints('TEST', 'catch', 3);
        $this->assertEquals(24, $points); // 3 catches * 8 points each = 24

        // Test T10 format
        $points = CricketPointsService::calculateCricketBonusPoints('T10', 'catch', 1);
        $this->assertEquals(8, $points); // 1 catch * 8 points = 8
    }

    /**
     * Test stumping points calculation for different formats
     */
    public function test_stumping_points_calculation()
    {
        // Test T20 format
        $points = CricketPointsService::calculateCricketBonusPoints('T20', 'stumping', 1);
        $this->assertEquals(12, $points); // 1 stumping * 12 points = 12

        // Test ODI format
        $points = CricketPointsService::calculateCricketBonusPoints('ODI', 'stumping', 2);
        $this->assertEquals(24, $points); // 2 stumpings * 12 points each = 24

        // Test Test format
        $points = CricketPointsService::calculateCricketBonusPoints('TEST', 'stumping', 1);
        $this->assertEquals(12, $points); // 1 stumping * 12 points = 12

        // Test T10 format
        $points = CricketPointsService::calculateCricketBonusPoints('T10', 'stumping', 1);
        $this->assertEquals(12, $points); // 1 stumping * 12 points = 12
    }

    /**
     * Test run out points calculation for different formats
     */
    public function test_run_out_points_calculation()
    {
        // Test T20 format
        $points = CricketPointsService::calculateCricketBonusPoints('T20', 'run_out', 1);
        $this->assertEquals(6, $points); // 1 run out * 6 points = 6

        // Test ODI format
        $points = CricketPointsService::calculateCricketBonusPoints('ODI', 'run_out', 2);
        $this->assertEquals(12, $points); // 2 run outs * 6 points each = 12

        // Test Test format
        $points = CricketPointsService::calculateCricketBonusPoints('TEST', 'run_out', 1);
        $this->assertEquals(6, $points); // 1 run out * 6 points = 6

        // Test T10 format
        $points = CricketPointsService::calculateCricketBonusPoints('T10', 'run_out', 3);
        $this->assertEquals(18, $points); // 3 run outs * 6 points each = 18
    }

    /**
     * Test zero fielding actions
     */
    public function test_zero_fielding_actions()
    {
        // Test zero catches
        $points = CricketPointsService::calculateCricketBonusPoints('T20', 'catch', 0);
        $this->assertEquals(0, $points);

        // Test zero stumpings
        $points = CricketPointsService::calculateCricketBonusPoints('ODI', 'stumping', 0);
        $this->assertEquals(0, $points);

        // Test zero run outs
        $points = CricketPointsService::calculateCricketBonusPoints('TEST', 'run_out', 0);
        $this->assertEquals(0, $points);
    }

    /**
     * Test invalid format handling
     */
    public function test_invalid_format_handling()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unknown format: INVALID');
        
        CricketPointsService::calculateCricketBonusPoints('INVALID', 'catch', 1);
    }

    /**
     * Test unknown fielding type
     */
    public function test_unknown_fielding_type()
    {
        $points = CricketPointsService::calculateCricketBonusPoints('T20', 'unknown_type', 1);
        $this->assertEquals(0, $points);
    }

    /**
     * Test combined fielding points calculation
     */
    public function test_combined_fielding_points()
    {
        $format = 'T20';
        
        // Calculate individual fielding points
        $catchPoints = CricketPointsService::calculateCricketBonusPoints($format, 'catch', 2);
        $stumpingPoints = CricketPointsService::calculateCricketBonusPoints($format, 'stumping', 1);
        $runOutPoints = CricketPointsService::calculateCricketBonusPoints($format, 'run_out', 1);
        
        $totalFieldingPoints = $catchPoints + $stumpingPoints + $runOutPoints;
        
        // Expected: (2*8) + (1*12) + (1*6) = 16 + 12 + 6 = 34
        $this->assertEquals(34, $totalFieldingPoints);
    }
}
