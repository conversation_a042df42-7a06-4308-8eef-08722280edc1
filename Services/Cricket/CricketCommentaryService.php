<?php

namespace App\Services\Cricket;

use App\global_constants;
use App\Services\CurlService;
use m<PERSON>ssen\BreadcrumbsBundle\Breadcrumbs as Breadcrumb;
use <PERSON><PERSON>, <PERSON>, Config, <PERSON><PERSON>, <PERSON><PERSON>, DB, File, Hash, Input, Mail, mongoDate, Redirect, Response, Session, URL, View, Validator;
use mPDF;

class CricketCommentaryService
{
    public static function getScorePerBall($matchKey, $inning, $sport_type = 1)
    {
        $apiToken = CRICKET_API_TOKEN;
        $url = CRICKET_API_URL . "/v2/matches/{$matchKey}/innings/{$inning}/commentary?token={$apiToken}";
        try {
            $response = CurlService::makeGetRequest($url);  //call curl get request 
            $result = json_decode($response);

            #when api token is expired then its return unauthorized error
            if ($result->status == 'unauthorized') {
                throw new \Exception($result->response);
            }

            if (json_last_error() !== JSON_ERROR_NONE) {
                // \Log::error('JSON decode error', ['error' => json_last_error_msg(), 'response' => $response]);
                $errorMsg = 'JSON decode error: ' . json_last_error_msg();
                throw new \Exception($errorMsg);
            }

            if (!$result || !isset($result->response->commentaries)) {
                // \Log::warning('Invalid API response', ['url' => $url, 'response' => $response]);
                $errorMsg = 'Invalid API response: ' . json_last_error_msg();
                throw new \Exception($errorMsg);
            }
            $batting_team_id = $result->response->inning->batting_team_id ?? '';
            $fielding_team_id = $result->response->inning->fielding_team_id ?? '';

            #Accept only event=ball,wicket
            $inning_commentaries = array_filter($result->response->commentaries, function ($commentary) {
                return $commentary->event == 'ball' || $commentary->event == 'wicket';
            });

            #total Inning commentary count
            $inning_commentary_count = count($inning_commentaries);

            #update Etag (suggested by Afzal to update this)
            self::UpdateEntityEtag($matchKey, $sport_type, $result->etag);
            #check Match details exist in cricket_commentary table

            $commentaryExistCount = DB::table("cricket_commentary")
                ->where(['match_key' => $matchKey, 'inning_number' => $inning])
                // ->select(DB::raw("COUNT(match_key) as match_key"))
                // ->groupBy('match_key')->count();
                ->count();

            $commentaryExistCount = $commentaryExistCount - 12;
            $commentaryExistCount = ($commentaryExistCount <= 0) ? 0 : $commentaryExistCount;

            #check incoming match commentary or DB commentary count same , in this case skipped all process
            // if($matchKey != '78616'){
                
                if ($commentaryExistCount == $inning_commentary_count) {
                    return $inning_commentaries;
                }
            // }

            // $start = $commentaryExistCount > 0 ? $commentaryExistCount + 1 : $commentaryExistCount;
            $start = $commentaryExistCount;
            $end = $inning_commentary_count;
            // dump("start =>>",$start,"end =>>",$end);
            $sliceCommentaryInsertdata = array_slice($inning_commentaries, $start, $end);

            // /Log::info('Start = '.$start.' || End = '.$end.' || sliceCommentaryInsertdata == '.json_encode($sliceCommentaryInsertdata));

            // if($matchKey == '78616'){
            //     $sliceCommentaryInsertdata = $inning_commentaries;
            // }

            // dd($sliceCommentaryInsertdata);

            // Get match slotes
            $matchSlotes = DB::table('match_slotes as ms')
                ->join('slotes_master as sm', 'sm.id', 'ms.slotes_master_id')
                ->where('ms.matchkey', $matchKey)
                ->where('ms.sport_type', 1)
                ->where('ms.fantasy_type_id', 7)
                ->where('sm.inning', $inning)
                ->whereNull('ms.deleted_at')
                ->selectRaw('ms.id as sloteId, sm.start_over, sm.end_over')
                ->orderBy('ms.id', 'ASC')
                ->groupBy('ms.id')
                ->get();

            $insertCommentaryData = [];
            $lastEventIds = DB::table("cricket_commentary")
                ->where(['match_key' => $matchKey, 'inning_number' => $inning])
                ->orderBy("id", "DESC")->limit(12)->pluck("event_id")->toArray();

            foreach ($sliceCommentaryInsertdata as $commentary) {
                #get batsman details basis of batsman id
                $filteredBatsman = collect($commentary->batsmen)->firstWhere('batsman_id', $commentary->batsman_id);

                $balls_faced = $filteredBatsman->balls_faced ?? 0;
                $matchplayers_role = $filteredBatsman->role ?? '';
                $commentaryData = [
                    'match_key'   => $matchKey,
                    // 'sport_type'  => $sport_type,
                    'event_id'        => $commentary->event_id,
                    'match_event'        => $commentary->event,
                    'inning_number'      => $inning,
                    'batting_team_id'        => $batting_team_id,
                    'fielding_team_id'        => $fielding_team_id,
                    'batsman_id'        => $commentary->batsman_id ?? '',
                    'bat_run'        => $commentary->bat_run ?? 0,
                    'balls_faced'        => $balls_faced ?? 0,
                    'bowler_id'        => $commentary->bowler_id ?? '',
                    'match_over'        => $commentary->over ?? 0,
                    'match_ball'        => $commentary->ball ?? 0,
                    'match_score'        => $commentary->score ?? 0,
                    'match_run'        => $commentary->run ?? 0,
                    'match_commentary'        => $commentary->commentary ?? '',
                    'issuperover'        => isset($result->response->inning->issuperover) ? $result->response->inning->issuperover : false,
                    'noball_dismissal'        =>  isset($commentary->founoball_dismissalr) ? ($commentary->noball_dismissal ? 'true' : 'false') : 'false',
                    'match_timestamp'        => $commentary->timestamp ?? 0,
                    'noball_run'        => $commentary->noball_run ?? 0,
                    'wide_run'        => $commentary->wide_run ?? 0,
                    'bye_run'        => $commentary->bye_run ?? 0,
                    'legbye_run'        => $commentary->legbye_run ?? 0,
                    'noball' => isset($commentary->noball) ? (bool)$commentary->noball : false,
                    'wideball' => isset($commentary->wideball) ? (bool)$commentary->wideball : false,
                    'six' => isset($commentary->six) ? (bool)$commentary->six : false,
                    'four' => isset($commentary->four) ? (bool)$commentary->four : false,
                    'wicket_batsman_id'        => $commentary->wicket_batsman_id ?? 0,
                    'how_out'        => $commentary->how_out ?? '',
                    'out_batsman_runs'        => $commentary->batsman_runs ?? '',
                    'out_batsman_balls'        => $commentary->batsman_balls ?? '',
                    'first_fielder_id'        => '',
                    'second_fielder_id'        => '',
                    'third_fielder_id'        => '',
                    'first_fielder_point'        => 0,
                    'second_fielder_point'        => 0,
                    'third_fielder_point'        => 0,
                    'second_fielder_runout'        => false,
                    'third_fielder_runout'        => false,
                    'first_fielder_runout'        => false,
                    'runout_thrower_id'        => 0,
                    'runout_thrower'        => false,
                    'runout_thrower_point'        => 0,
                    'runout_catcher_id'        => 0,
                    'runout_catcher'        => false,
                    'runout_catcher_point'        => 0,
                    'bowler_points'        => 0,
                    'bowler_wicket'        => false,
                    'first_fielder_caught' => 0,
                    'bowler_bowled' => false,
                    'bowler_bowled_points' => 0,
                    'lbw_bowled_points' => 0,
                    'lbw_bowled' => false,
                    'runout_fielder_id' => '',
                    'runouts' => false,
                    'runouts_point' => 0,
                    'runouts_point' => 0,
                ];

                if ($commentary->event == 'ball') {
                    $batsmanPoint = self::getPlayerPoint($commentary->bat_run);
                    $commentaryData['batsman_points'] = $batsmanPoint;
                } else if ($commentary->event == 'wicket') {  // Process Wicket events

                    #get batsman details basis of batsman_id
                    #dismissal type = runout,caught,bowled,lbw
                    $batsman = self::findBatsmanById($result->response->inning->batsmen, $commentary->batsman_id);
                    if ($batsman->dismissal == 'runout') {

                        #when runout help of three fielder
                        if (!empty($batsman->first_fielder_id) && !empty($batsman->second_fielder_id) && !empty($batsman->third_fielder_id)) {
                            $commentaryData['second_fielder_id'] = $batsman->second_fielder_id;
                            $commentaryData['second_fielder_point'] = RUNOUTSTHROWER_POINTS;
                            $commentaryData['third_fielder_id'] = $batsman->third_fielder_id;
                            $commentaryData['third_fielder_point'] = RUNOUTSCATCHER_POINTS;
                            $commentaryData['second_fielder_runout'] = true;
                            $commentaryData['third_fielder_runout'] = true;

                            $commentaryData['runout_thrower_id'] = $batsman->second_fielder_id;
                            $commentaryData['runout_thrower'] = true;
                            $commentaryData['runout_thrower_point'] = RUNOUTSTHROWER_POINTS;

                            $commentaryData['runout_catcher_id'] = $batsman->third_fielder_id;
                            $commentaryData['runout_catcher'] = true;
                            $commentaryData['runout_catcher_point'] = RUNOUTSCATCHER_POINTS;
                        } elseif (!empty($batsman->first_fielder_id) && !empty($batsman->second_fielder_id)) {
                            #when runout help of two fielder
                            #first_fielder_id = runout_thrower ,second_fielder_id=runout_catcher
                            $commentaryData['first_fielder_id'] = $batsman->first_fielder_id;
                            $commentaryData['first_fielder_point'] = RUNOUTSTHROWER_POINTS;
                            $commentaryData['second_fielder_id'] = $batsman->second_fielder_id;
                            $commentaryData['second_fielder_point'] = RUNOUTSCATCHER_POINTS;
                            $commentaryData['first_fielder_runout'] = true;
                            $commentaryData['second_fielder_runout'] = true;

                            $commentaryData['runout_thrower'] = true;
                            $commentaryData['runout_thrower_id'] = $batsman->first_fielder_id;
                            $commentaryData['runout_thrower_point'] = RUNOUTSTHROWER_POINTS;
                            $commentaryData['runout_catcher'] = true;
                            $commentaryData['runout_catcher_id'] = $batsman->second_fielder_id;
                            $commentaryData['runout_catcher_point'] = RUNOUTSCATCHER_POINTS;
                        } else {
                            #when runout by direct throw
                            // $commentaryData['bowler_id'] = $commentary->bowler_id;
                            $commentaryData['first_fielder_id'] = $batsman->first_fielder_id;
                            $commentaryData['first_fielder_point'] = RUNOUTS_POINTS;
                            $commentaryData['first_fielder_runout'] = true;

                            $commentaryData['runout_fielder_id'] = $batsman->first_fielder_id;
                            $commentaryData['runouts'] = true; //extra add on
                            $commentaryData['runouts_point'] = RUNOUTS_POINTS; //extra add on
                        }
                    } elseif ($batsman->dismissal == 'caught') {
                        #when ball by baller and catch by first fielder
                        // $commentaryData['bowler_id'] = $commentary->bowler_id;
                        $commentaryData['bowler_points'] = BOWLER_WICKET_POINT;
                        $commentaryData['first_fielder_id'] = $batsman->first_fielder_id;
                        $commentaryData['first_fielder_point'] = FIELDER_CATCH_POINT;
                        $commentaryData['bowler_wicket'] = true;
                        $commentaryData['first_fielder_caught'] = true;
                    } elseif ($batsman->dismissal == 'bowled') {
                        #when bowled and catch by baller 
                        #when bowler bowled by direct hitting wicket , in this case its recevied this Points BOWLER_WICKET_POINT + LBW_BOWLED;
                        $lbw_points = CricketPointsService::calculateCricketBonusPoints('T20', 'lbw_bowled', 1);

                        // $commentaryData['bowler_id'] = $commentary->bowler_id;
                        $commentaryData['bowler_points'] = BOWLER_WICKET_POINT + $lbw_points;
                        $commentaryData['bowler_wicket'] = true;
                        $commentaryData['bowler_bowled'] = true;
                        $commentaryData['bowler_bowled_points'] = LBW_BOWLED;
                    } elseif ($batsman->dismissal == 'lbw') {
                        #when bowler bowled of batsman by lbw, in this case its recevied this Points BOWLER_WICKET_POINT + LBW_BOWLED;
                        #when bowled and catch by baller 
                        $lbw_points = CricketPointsService::calculateCricketBonusPoints('T20', 'lbw_bowled', 1);

                        // $commentaryData['bowler_id'] = $commentary->bowler_id;
                        $commentaryData['bowler_points'] = BOWLER_WICKET_POINT + $lbw_points;
                        $commentaryData['lbw_bowled_points'] = $lbw_points;
                        $commentaryData['lbw_bowled'] = true;
                        $commentaryData['bowler_wicket'] = true;
                    }
                    // $commentaryData['bowler_id'] = $batsman->bowler_id;
                    $commentaryData['dismissal'] = $batsman->dismissal;

                    // if($matchKey == '78609'){

                        if(!empty($matchSlotes)){

                            self::updateSlotPlayers($matchKey, $inning, $batting_team_id, $commentary->over, $commentary->ball, $commentary->wicket_batsman_id, $matchSlotes);
                        }
                    // }
                }
                $commentaryData['match_event'] = $commentary->event;
                
                // Check if record inserted already then update record
                if(in_array($commentary->event_id, $lastEventIds)){
                    // if($matchKey == '85812'){
                    //     \Log::info('event_id'.$commentary->event_id);
                    // }
                    DB::table('cricket_commentary')->where('event_id', $commentary->event_id)->update($commentaryData);
                } else {
                    // Insert new record
                    DB::table('cricket_commentary')->insert($commentaryData);
                }

                // if($matchKey != '78616'){

                    // #store cricket commentary details
                    // DB::table('cricket_commentary')->insert($commentaryData);
                // }
                // $insertCommentaryData[] = $commentaryData;
            }

            // $batchSize = 500;
            // foreach (array_chunk($insertCommentaryData, $batchSize) as $batch) {
            //     DB::table('cricket_commentary')->insert($batch);
            // }

        } catch (\Exception $e) {

            \Log::info('Error on player out >> Log on got out any player = '.$e->getMessage());

            // \Log::error('Exception occurred in getScorePerBall', [
            //     'exception' => $e->getMessage(),
            //     'file' => $e->getFile(),
            //     'line' => $e->getLine(),
            // ]);
            throw new \Exception($e->getMessage());
            // return [];
        }
    }

    // Update slot player on wicket
    public static function updateSlotPlayers($matchKey, $inning, $batting_team_id, $over, $ball, $player_key, $matchSlotes){

        date_default_timezone_set("Asia/Kolkata");
        // if($matchKey == '88928'){
        //     \Log::info('Step 1. updateSlotPlayers => Match = '.$matchKey.' || Inning = '.$inning.' || Team id = '.$batting_team_id.' || Over = '.$over.' || Ball = '.$ball.' || Player key = '.$player_key);   
        // }

        try {
                
            $matchKey = strval($matchKey);
            $currentSlot = 0;

            foreach($matchSlotes as $slot){

                $start_over = $slot->start_over - 1;
                $end_over = $slot->end_over - 1;

                if($over >= $start_over && $over <= $end_over){
                    $currentSlot = $slot->sloteId;
                    continue;
                }
            }

            // if($matchKey == '88928'){
            //     \Log::info('Step 2. Current Slot = '.$currentSlot);
            // }

            // Get player details
            $playerDetail = DB::table('matchplayers')->where('matchkey', $matchKey)->where('sport_type', 1)->where('player_key', $player_key)->first(['id']);

            if(empty($playerDetail->id)){
                return 0;
            }

            // if($matchKey == '88928'){
            //     \Log::info('Step 3. player_key = '. $player_key.' || Player Id = '.$playerDetail->id);
            // }

            // If player not exists in next slotes
            $meExistsInSlotes = DB::table('match_slot_players')
                                ->where('matchkey', $matchKey)
                                ->where('match_player_id', $playerDetail->id)
                                ->whereNull('deleted_at')
                                ->where('slotes_id', '>', $currentSlot)
                                ->whereIn('slotes_id', $matchSlotes->pluck('sloteId')->toArray())
                                ->pluck('slotes_id')->toArray();

            // if($matchKey == '88928'){
            //     \Log::info('Step 4. meExistsInSlotes = '.json_encode($meExistsInSlotes));
            // }

            if(empty($meExistsInSlotes)){
                return 0;
            }

            $teamId = DB::table('teams')->where('team_key', $batting_team_id)->where('sport_type', 1)->first()->id;

            // if($matchKey == '88928'){
            //     \Log::info('Step 5. Team Id = '.$teamId);
            // }

            if(empty($teamId)){
                return 0;
            }

            $mySlotes = $meExistsInSlotes;
            $mySlotes[] = $currentSlot;

            // Get match playing 11
            $matchPlaying11 = DB::table('match_playing11')
                ->where('match_key', $matchKey)
                ->where('sport_type', 1)
                ->value('player_ids');

            if(empty($matchPlaying11)){
                return 0;
            }

            $playerIds = unserialize($matchPlaying11);

            // if($matchKey == '88928'){
            //     \Log::info('Step 6. Line up players = '.json_encode($teamId));
            // }

            // Remove player from slotes
            $playerRemoved = DB::table('match_slot_players')
                ->where('matchkey', $matchKey)
                ->whereIn('slotes_id', $meExistsInSlotes)
                ->where('match_player_id', $playerDetail->id)
                ->update(['deleted_at' => date('Y-m-d H:i:s')]);

            // if($matchKey == '88928'){
            //     \Log::info('Step 7. Player removed from slotes = '.$playerRemoved);
            // }

            foreach($meExistsInSlotes as $mSlot){

                // if($matchKey == '88928'){
                //     \Log::info('Step 8. Slot Id = '.$mSlot);
                // }

                $newPlayer = DB::table('matchplayers as mp')
                            ->join('players as ps', 'ps.id', '=', 'mp.playerid')
                            ->join('teams as t', 't.id', '=', 'ps.team')
                            ->where('mp.matchkey', $matchKey)
                            ->where('mp.sport_type', 1)
                            ->where('t.id', $teamId)
                            ->whereIn('mp.playerid', $playerIds)
                            ->whereNotExists(function ($query) use ($mSlot) {
                                $query->selectRaw('1')
                                    ->from('match_slot_players as msp')
                                    ->whereColumn('msp.match_player_id', 'mp.id')
                                    ->where('msp.slotes_id', $mSlot); 
                            })
                            ->select('mp.id')
                            ->orderBy('mp.selected_percent', 'DESC')
                            ->limit(1)
                            ->first();

                // if($matchKey == '88928'){
                //     \Log::info(' Step 9. New Player Id = '.$newPlayer->id);
                // }

                if(empty($newPlayer->id)){
                    continue;
                }

                DB::table('match_slot_players')->insert(['matchkey' => $matchKey, 'slotes_id' => $mSlot, 'match_player_id' => $newPlayer->id]);
            }

        } catch (\Exception $e) {
            // \Log::info('Matcheky 88928 Error on new player import while out any player in slot = '.$e->getMessage());
        }
    }

    #get batsmand Details basis of BatsmanID
    public static function findBatsmanById($batsmen, $batsmanId)
    {
        foreach ($batsmen as $batsman) {
            if ($batsman->batsman_id == $batsmanId) {
                return $batsman;
            }
        }
        return null;
    }

    public static function getPlayerPoint($bat_run)
    {
        $pointsMap = [
            1 => BATSMAN_PER_RUN_POINT,
            2 => BATSMAN_TWO_RUN_POINT,
            3 => BATSMAN_THREE_RUN_POINT,
            4 => BATSMAN_FOUR_RUN_POINT,
            6 => BATSMAN_SIX_RUN_POINT,
        ];
        return $pointsMap[$bat_run] ?? $bat_run;
    }

    #check Entity API Status , If Etag already exist or not according condition
    public static function UpdateEntityEtag($matchKey, $sport_type, $etag)
    {
        $entity_api_status = DB::table('entity_api_status')
            ->where([
                'matchkey' => $matchKey,
                'sport_type' => $sport_type,
                'api_type' => 1
            ])->first();
        if ($entity_api_status) {
            DB::table('entity_api_status')->where('id', $entity_api_status->id)->update(['etag' => $etag]);
            // Update by the primary key or unique identifier

        } else {
            DB::table('entity_api_status')->insert(['matchkey' => $matchKey, 'sport_type' => $sport_type, 'api_type' => 1, 'etag' => $etag]);
        }

        return $entity_api_status;
    }
}
