<?php

namespace App\Services\Sportz;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Services\CurlService;

class SportzInteractiveAPICallingService
{
    /** Vision11 client-id passed to every API call */
    protected static $clientId = 'c473b3f16d35';

    /** Mark<PERSON> saved in `listmatches.source` so we can easily purge later */
    protected static $source   = 'sportz';


    /**
     * Call Sportz schedule endpoint and return the “matches” array.
     * Keeps the logic isolated for easy mocking / testing.
     *
     * @param  int   $year
     * @return array
     * @throws \Exception if the endpoint fails or returns invalid JSON
     */
    //get Match list 
    public static function fetchSchedule($match_key,$sport_type,$year)
    {
        $url = "https://assets-vision11.sportz.io/cricket/v1/schedule?"
            . "year={$year}&is_deleted=false&timezone=0000"
            . "&match_id={$match_key}&is_live=true&is_upcoming=true&is_recent=false"
            . "&date_list=false&pagination=true&page_size=1&page_number=1"
            . "&lang=en&feed_format=json&client_id=" . self::$clientId;

        $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);

        if (!$json) {
            throw new Exception('Unable to fetch data from Sportz API');
        }

        $payload = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON error: ' . json_last_error_msg());
        }
        return $payload['data']['matches'] ?? [];
    }

    /**
     *Get Match Squad/Player  List basis of series_id and team_id.
     *team_id,series_id is required to get player List from Sportz
     */
    public static function fetchMatchSquad($team_id, $series_id)
    {
        try {
            $url = "https://assets-vision11.sportz.io/cricket/v1/series/squad?team_id=".$team_id."&series_id=".$series_id."&lang=en&feed_format=json&client_id=" . self::$clientId;
            // echo $url;die;
            $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);

            if (!$json) {
                throw new Exception('Unable to fetch data from Sportz API');
            }

            $payload = json_decode($json, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON error: ' . json_last_error_msg());
            }
            // return $payload['data']['squads']['teams']['team'][0]['players']['player'];
            return $payload['data']['squads'];
        } catch (\Throwable $e) {
            // Log the issue and bubble it up (or return [] if you prefer silent failure)
            Log::error('[Sportz] fetchMatchSquad failed: ' . $e->getMessage(), [
                'team_id' => $team_id,
                'series_id' => $series_id,
            ]);
            throw $e;
        }
    }
}
