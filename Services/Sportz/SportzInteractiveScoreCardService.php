<?php

namespace App\Services\Sportz;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Services\CurlService;
use App\Model\ListMatchDetails;
use App\Http\Controllers\Admin\SportsInteractiveMatchPointsController;
use App\Helpers\Helpers;
use App\Http\Controllers\Admin\MatchesController;

class SportzInteractiveScoreCardService
{
    /** Vision11 client-id passed to every API call */
    protected static $clientId = 'c473b3f16d35';

    /** Marker saved in `listmatches.source` so we can easily purge later */
    protected static $source   = 'sportz';


    public static function sportzCricketScoreUpdate($match_key, $sport_type)
    {
        
        date_default_timezone_set('Asia/Kolkata');
        $sportType = DB::table('sport_types')->where('id', $sport_type)->first();
        $sport_type = $sportType->sport_key;


        $findmatchtype = DB::table('listmatches')->where('matchkey', $match_key)->where('sport_type', 1)->first();

        $get_playing_11_new_res = self::get_playing_11_new($match_key, $sport_type);

        //scoreCard Data from sportz
        $PointController = new SportsInteractiveMatchPointsController();
        $request = new \Illuminate\Http\Request();
        $request->merge([
            'game_id' => $match_key,
            'lang' => 'en',
            'feed_format' => 'json',
        ]);
        $response = $PointController->calculate($request);
        $scoreCard = $response->getData(true);
        $sportType = DB::table('sport_types')->where('sport_key', $sport_type)->first();

        $match_detail = $giveresresult = $scoreCard;

        $checkpre = DB::table('matchruns')->where('matchkey', $match_key)->where('sport_type', 1)->first();


        if (empty($checkpre)) {

            $matchdata['matchkey'] = $match_key;

            $matchdata['sport_type'] = 1;

            $matchdata['teams1'] = $findmatchtype->team1display??'';

            $matchdata['teams2'] = $findmatchtype->team2display??'';

            $matchdata['winning_status'] = 0;

            if (!empty($giveresresult['match_details']['innings'])) {
                $gettestscore1 = 0;
                $gettestscore2 = 0;
                $gettestwicket1 = 0;
                $gettestwicket2 = 0;
                $gettestover1 = 0;
                $gettestover2 = 0;
                if (!empty($giveresresult['match_details']['innings'])) {
                    $scoreInning1 = @$giveresresult['match_details']['innings'][0]['Total'] . "/" . @$giveresresult['match_details']['innings'][0]['Wickets'];
                    $scoreInning2 = @$giveresresult['match_details']['innings'][1]['Total'] . "/" . @$giveresresult['match_details']['innings'][1]['Wickets'];

                    $runs_wick_0 = $scoreInning1;
                    $runs_wick_1 = $scoreInning2;


                    $gettestscore1 = $giveresresult['match_details']['innings'][0]['Total'];
                    $gettestwicket1 = $giveresresult['match_details']['innings'][0]['Wickets'];

                    $gettestscore2 = @$giveresresult['match_details']['innings'][1]['Total'];
                    $gettestwicket2 = @$giveresresult['match_details']['innings'][1]['Wickets'];

                    $gettestover1 = 0;
                    $gettestover2 = 0;

                    $matchdata['wickets1'] = $gettestwicket1;
                    $matchdata['wickets2'] = $gettestwicket2 ? $gettestwicket2 : 0;
                    $matchdata['overs1'] = $gettestover1;
                    $matchdata['overs1'] = '';
                    $matchdata['overs2'] = $gettestover2 ? $gettestover2 : 0;
                    $matchdata['overs2'] = '';
                    $matchdata['runs1'] = $gettestscore1;
                    $matchdata['runs2'] = $gettestscore2 ? $gettestscore2 : 0;
                    // print_r($matchdata); exit;
                }
            } else {
                $matchdata['winning_status'] = 0;
                $matchdata['wickets1'] = 0;
                $matchdata['wickets2'] = 0;
                $matchdata['overs1'] = 0;
                $matchdata['overs2'] = 0;
                $matchdata['runs1'] = 0;
                $matchdata['runs2'] = 0;
            }
            DB::table('matchruns')->insert($matchdata);
        } else {

            $matchdata1['matchkey'] = $match_key;

            $teamIds = DB::table('teams')->whereIn('team_key', [$giveresresult['match_details']['team_home']['id'], $giveresresult['match_details']['team_away']['id']])->where('sport_type', $findmatchtype->sport_type)->pluck('id', 'team_key')->toArray();

            $teams1 = ($giveresresult['match_details']['team_home']['id'] == $findmatchtype->team1) ? $findmatchtype->team1display : $findmatchtype->team2display;

            $teams2 = ($giveresresult['match_details']['team_away']['id'] == $findmatchtype->team2) ? $findmatchtype->team2display : $findmatchtype->team1display;

            $matchdata1['teams1'] = $teams1;

            $matchdata1['teams2'] = $teams2;

            $matchdata1['winning_status'] = 0;

            if (!empty($giveresresult['match_details']['innings'])) {
                $gettestscore1 = 0;
                $gettestscore2 = 0;
                $gettestwicket1 = 0;
                $gettestwicket2 = 0;
                $gettestover1 = 0;
                $gettestover2 = 0;
                if (!empty($giveresresult['match_details']['innings']['0'])) {



                    //Team A and Team B Over section
                    $overs1 = $giveresresult['match_details']['team_home']['score']['overs']; //team a overs
                    $overs2 = $giveresresult['match_details']['team_away']['score']['overs']; //team a overs;


                    // Team A and Team B Run and Wicket
                    $gettestscore1 = $giveresresult['match_details']['team_home']['score']['runs'] ?? 0; //team a run
                    $gettestwicket1 = $giveresresult['match_details']['team_home']['score']['wickets'] ?? 0; //team a Wicket

                    $gettestscore2 = $giveresresult['match_details']['team_away']['score']['runs']; //team b run
                    $gettestwicket2 = $giveresresult['match_details']['team_away']['score']['wickets'];

                    $gettestover1 = (float) $overs1 ?? 0;
                    $gettestover2 = (float) $overs2 ?? 0;

                    $matchdata1['wickets1'] = $gettestwicket1;
                    $matchdata1['wickets2'] = $gettestwicket2;
                    $matchdata1['overs1'] = $gettestover1;
                    $matchdata1['overs2'] = $gettestover2;
                    $matchdata1['runs1'] = $gettestscore1;
                    $matchdata1['runs2'] = $gettestscore2;
                    $matchdata1['sport_type'] = 1;
                }
            } else {
                $matchdata1['wickets1'] = 0;
                $matchdata1['wickets2'] = 0;
                $matchdata1['overs1'] = 0;
                $matchdata1['overs2'] = 0;
                $matchdata1['runs1'] = 0;
                $matchdata1['runs2'] = 0;
            }

            DB::table('matchruns')->where('matchkey', $match_key)->where('sport_type', 1)->update($matchdata1);
        }

        if (!empty($match_detail)) {
            // $match_status = $match_detail->status; // 1 = scheduled , 2 = completed, 3 = Live , 4 = canceled

            // if ($match_status == 2 && $match_detail->verified == 'true') {
            //     $getmtdatastatus['status'] = 'completed';
            //     $getmtdatastatus['is_verified'] = 1;
            // }

            // if ($match_status == 3) {

            //     $getmtdatastatus['status'] = 'started';

            //     // Get batting team name and store in listmatches >> winner_team column to show current batting team
            //     $innings = $match_detail->innings;

            //     $battingTeamId = (count($innings) > 0) ? $innings[count($innings) - 1]->batting_team_id : $innings[0]->batting_team_id;

            //     $teamDetails = DB::table('teams')->where('team_key', $battingTeamId)->where('sport_type', $findmatchtype->sport_type)->select('id')->first();

            //     // $battingTeam = ($battingTeamId == $teamAid) ? $match_detail->teama->short_name : $match_detail->teamb->short_name;

            //     $battingTeam = ($teamDetails->id == $findmatchtype->team1) ? $findmatchtype->team1display : $findmatchtype->team2display;

            //     $getmtdatastatus['winner_team'] = $battingTeam;
            // }

            // $getmtdatastatus['status'] = $mainarrayget['status'];

            // if (@$getmtdatastatus['status'] == 'completed') {

            //     $getmtdatastatus['final_status'] = 'IsReviewed';

            //     // Check if winner is exists and match is in reviewed state
            //     if (isset($giveresresult->winning_team_id) && @$giveresresult->winning_team_id != "") {

            //         $teamDetails = DB::table('teams')->where('team_key', $giveresresult->winning_team_id)->where('sport_type', $findmatchtype->sport_type)->select('id')->first();

            //         // $winnerTeam = ($giveresresult->winning_team_id == $teamAid) ? $giveresresult->teama->short_name : $giveresresult->teamb->short_name;

            //         $winnerTeam = ($teamDetails->id == $findmatchtype->team1) ? $findmatchtype->team1display : $findmatchtype->team2display;

            //         $getmtdatastatus['winner_team'] = $winnerTeam;
            //     }
            // }

            // if (!empty(@$getmtdatastatus['status']) || !is_null(@$getmtdatastatus['status'])) {

            //     DB::table('listmatches')->where('matchkey', $match_key)->where('sport_type', 1)->update($getmtdatastatus);

            //     $findIsSecondInning = DB::table('listmatches')->where('matchkey', $match_key)->where('second_inning_allow', 1)->first();

            //     if (!empty($findIsSecondInning)) {
            //         DB::table('listmatches')->where('matchkey', $match_key . '_2')->where('sport_type', 1)->update($getmtdatastatus);
            //     }
            // }


            //for point calculation 
            if (count($giveresresult['match_player_points']) > 0) {

                $allResultMatchesPlayers = DB::table("result_matches")
                    ->where(function ($q) use ($match_key) {
                        $q->where("match_key", $match_key)->orWhere("match_key", $match_key . '_2');
                    })
                    ->where("sport_type", 1)
                    ->where("fantasy_type_id", FANTASY_TYPE_CLASSIC)
                    ->get();


                //Store Empty points
                if ($allResultMatchesPlayers->isNotEmpty()) {

                    foreach ($allResultMatchesPlayers as $rm_key => $rm_value) {
                        $updateResMat['runs'] = 0;
                        $updateResMat['bball'] = 0;
                        $updateResMat['fours'] = 0;
                        $updateResMat['six'] = 0;
                        $updateResMat['batting'] = 0;
                        $updateResMat['strike_rate'] = 0;
                        $updateResMat['duck'] = 0;
                        $updateResMat['out_str'] = NULL;
                        $updateResMat['starting11'] = 0;
                        $updateResMat['balls'] = 0;
                        $updateResMat['maiden_over'] = 0;
                        $updateResMat['wicket'] = 0;
                        $updateResMat['extra'] = 0;
                        $updateResMat['overs'] = 0;
                        $updateResMat['grun'] = 0;
                        $updateResMat['lbw_bowled'] = 0;
                        $updateResMat['balldots'] = 0;
                        $updateResMat['bowling'] = 0;
                        $updateResMat['economy_rate'] = 0;
                        $updateResMat['catch'] = 0;
                        $updateResMat['stumbed'] = 0;
                        $updateResMat['runouts'] = 0;
                        $updateResMat['runout_catcher'] = 0;
                        $updateResMat['runout_thrower'] = 0;

                        if (in_array($rm_value->player_key, $giveresresult['match_player_points'])) {
                            $updateResMat['starting11'] = 1;
                        } else {
                            $updateResMat['starting11'] = 0;
                        }

                        DB::table("result_matches")->where("id", $rm_value->id)->update($updateResMat);
                    }
                }


                // echo "<pre>";print_r($giveresresult['match_player_points']);die;
                foreach ($giveresresult['match_player_points'] as $playing_player) {

                    $datasv = array();

                    $player_id = $playing_player['player_key'];

                    $datasv['player_key'] = $player_id;

                    $datasv['starting11'] = 1;
                    $datasv['fantasy_type_id'] = FANTASY_TYPE_CLASSIC;

                    $datasv['match_key'] = $match_key;

                    $datasv['innings'] = 1;


                    $player = DB::connection('mysql-write')->table('matchplayers')->join('players', 'players.id', '=', 'matchplayers.playerid')->where('matchkey', $match_key)->where('matchplayers.sport_type', 1)->select('matchplayers.*', 'players.player_key', 'players.role as playerrole')->where('players.player_key', $player_id)->first();

                    if (!empty($player)) {



                        $datasv['player_id'] = $player->playerid;


                        $findplayerex = DB::connection('mysql-write')->table('result_matches')->where('player_key', $player_id)
                            ->where('match_key', $match_key)
                            ->where('fantasy_type_id', FANTASY_TYPE_CLASSIC)
                            ->where('innings', 1)->select('id')->first();


                        if (empty($findplayerex)) {


                            try {

                                DB::table('result_matches')->insert($datasv);
                            } catch (\Illuminate\Database\QueryException $error) {

                                dd($error->getMessage());

                                // Note any method of class PDOException can be called on $ex.

                            }
                        }
                    }
                }


                $innings = $giveresresult['match_details']['innings'];

                if (is_array($innings)) {
                    DB::table('matchruns')->where('matchkey', $match_key)->where('sport_type', 1)
                        ->update(["match_time" => count($innings)]);
                }

                //Match Player Loop 
                foreach ($giveresresult['match_player_points'] as $key => $value2) {
                    $inning_number = $value2['inning_number'];
                    $inning_number_new = $value2['inning_number'];

                    if ($inning_number == 1 || $inning_number == 2) {

                        $inning_number = 1;
                    } else if ($inning_number == 3 || $inning_number == 4) {

                        $inning_number = 2;
                    }


                    //Points Calculation

                    $datasv = array();

                    $player_id = $value2['player_key'];
                    $player = DB::table('matchplayers')->join('players', 'players.id', '=', 'matchplayers.playerid')->where('matchkey', $match_key)->where('matchplayers.sport_type', 1)->select('matchplayers.*', 'players.player_key', 'matchplayers.role as playerrole')->where('players.player_key', $player_id)->first();


                    if (!empty($player)) {

                        $catch = 0;
                        $runouts = 0;
                        $stumbed = 0;
                        $batdots = 0;
                        $balldots = 0;
                        $six = 0;
                        $runs = 0;
                        $fours = 0;
                        $miletone_run = 0;
                        $bball = 0;

                        $strike = 0;
                        $acstrike = 0;
                        $grun = 0;
                        $balls = 0;
                        $bballs = 0;
                        $maiden_over = 0;
                        $wicket = 0;
                        $extra = 0;
                        $overs = 0;

                        $economy = 0;
                        $duck = 0;
                        $mile_wicket = 0;
                         $lbwBowled=0;


                        $datasv['runs'] = $runs = (int) $runs + (int) $value2['run'];

                        $datasv['bball'] = $bball = (int) $bball + (int) $value2['balls'];

                        $datasv['fours'] = $fours = (int) $fours + (int) $value2['fours_count'];

                        $datasv['six'] = $six = (int) $six + (int) $value2['sixs_count'];

                        $datasv['batting'] = 1;

                        $datasv['strike_rate'] = $value2['strike_rate_per'];
                         $datasv['duck'] =  0;

                          $datasv['out_str'] = "out";
                         
                        // if ($value2->how_out != 'Not out') {

                        //     if ($runs == 0 && $player->playerrole != 'bowler' && !empty($value2->dismissal)) {

                        //         $datasv['duck'] = $duck = 1;
                        //     } else {

                        //         $datasv['duck'] = $duck = 0;
                        //     }

                        //     $datasv['out_str'] = $value2->how_out;
                        // }

                        $datasv['match_key'] = $match_key;

                        $datasv['player_key'] = $player_id;

                        $datasv['player_id'] = $player->playerid;

                        $datasv['innings'] = $inning_number;

                        $datasv['starting11'] = 1;

                        //Bowler
                        $datasv['balls'] = $balls = $balls + (round((int) $value2['balls_bowled']) * 6);

                        $datasv['maiden_over'] = $maiden_over = (int) $maiden_over + (int) $value2['maidens_count'];

                        $datasv['wicket'] = $wicket = (int) $wicket + (int) $value2['wickets_count'];

                        $datasv['extra'] = $extra = $extra + (int) $value2['no_ball'] + (int) $value2['wides'];

                        $datasv['overs'] = $overs = (is_numeric($overs) ? $overs : 0) + (is_numeric($value2['overs']) ? $value2['overs'] : 0);

                        $datasv['grun'] = $grun = (int) $grun + (int) $value2['bowler_runs_conceded'];

                        $datasv['lbw_bowled'] = $lbwBowled = (int) $lbwBowled + ((int) $value2['lbw_bowled_count']);

                        $datasv['balldots'] = $balldots = (int) $balldots + (int) $value2['dots'];

                        $datasv['bowling'] = 1;

                        $datasv['economy_rate'] = $value2['economy_rate_per'];

                      //Fielder Point 
                      $datasv['catch'] = $catch = $catch + $value2['catch_count'];

                $datasv['stumbed'] = $stumbed = $stumbed + $value2['stumped_count'];

                $datasv['runouts'] = $runouts = $runouts + $value2['run_outs_count'];

                // $thrower = $value2->runout_thrower;
                // $hitter = $value2->runout_catcher;

                // $datasv['runout_catcher'] = $hitter;
                // $datasv['runout_thrower'] = $thrower;

                        
                        $findplayerex = DB::connection('mysql-write')->table('result_matches')->where('player_key', $player_id)
                            ->where('match_key', $match_key)
                            ->where('fantasy_type_id', FANTASY_TYPE_CLASSIC)
                            ->where('innings', $inning_number)->select('id')->first();

                        if (!empty($findplayerex)) {

                            DB::table('result_matches')->where('id', $findplayerex->id)->update($datasv);
                        } else {

                            try {

                                DB::table('result_matches')->insert($datasv);
                            } catch (\Illuminate\Database\QueryException $ex) {
                            }
                        }

                    }
                }

                // import match points 
                $matchController = new MatchesController();
                    $datasv['innings'] = 1;


                    $player = DB::connection('mysql-write')->table('matchplayers')->join('players', 'players.id', '=', 'matchplayers.playerid')->where('matchkey', $match_key)->where('matchplayers.sport_type', 1)->select('matchplayers.*', 'players.player_key', 'players.role as playerrole')->where('players.player_key', $player_id)->first();

                $showpoints = $matchController->player_point($match_key, $findmatchtype->format, "CRICKET", FANTASY_TYPE_CLASSIC);
            }
        }
    }



    public static function get_playing_11_new($matchkey, $sport_type = CRICKET)
    {
        $PointController = new SportsInteractiveMatchPointsController();
         $findmatchtype = DB::table('listmatches')->where('matchkey', $matchkey)->where('sport_type', 1)->select('format', 'second_inning', 'team1', 'team2', 'team1display', 'team2display', 'sport_type')->first();
        $request = new \Illuminate\Http\Request();
        $request->merge([
            'game_id' => $matchkey,
            'lang' => 'en',
            'feed_format' => 'json',
        ]);
        $response = $PointController->calculate($request);
        $scoreCard = $response->getData(true);
        $sportType = DB::table('sport_types')->where('sport_key', $sport_type)->first();
        $findMatchDetails = DB::table('listmatches_details')->where('matchkey', strval($matchkey))->where('sport_type', $sportType->id)->select('enable_short_fantasy')->first();

        

        $finalplayingteams = [];

        if (!empty($scoreCard)) {
            $match_details = $scoreCard['match_details'];
            $team1 = $match_details['team_home']['id'];
            $team2 = $match_details['team_away']['id'];
            $playerAndTeam = [];
            $players = array();
            $linedUpPlayerIds = [];
            $teams = array();

            $i = 0;

            $finalplayingteams = $scoreCard['match_player_points'];

            foreach ($finalplayingteams as $key => $playingTeam) {

                $fl = $playingTeam['player_key']; //playing player team

                //get Player Details
                $player_details = DB::table('players')
                    ->join('matchplayers', 'players.id', '=', 'matchplayers.playerid')
                    ->join('teams', 'players.team', '=', 'teams.id')
                    ->where('players.player_key', $fl)
                    ->where('players.sport_type', $sportType->id)
                    ->where('matchplayers.sport_type', $sportType->id)
                    ->where('matchplayers.matchkey', strval($matchkey))
                    ->select('players.id', 'players.team', 'matchplayers.name', 'matchplayers.id as matchplayers_rowid', 'teams.team as team_name')
                    ->first();

                if (!empty($player_details)) {
                    $players[] = $player_details->id;
                    $linedUpPlayerIds[] = $player_details->matchplayers_rowid;

                    $playerAndTeam[$key]['id'] = $player_details->id;
                    $playerAndTeam[$key]['name'] = $player_details->name;
                    $playerAndTeam[$key]['team_id'] = $player_details->team;
                    $playerAndTeam[$key]['team'] = $player_details->team_name;
                }
            }

            $playerLimit = Helpers::$playerLimit[$sportType->id];

            if (count($players) > 5 || (count($players) == 10)) {

                $linedUpAlready = array();
                // If match already lined up
                if (!empty($linedUpAlready)) {

                    $differenceIn = array_diff($players, unserialize($linedUpAlready->player_ids));
                    $differenceOut = array_diff(unserialize($linedUpAlready->player_ids), $players);

                    $newInPlayers = array_filter($playerAndTeam, function ($item) use ($differenceIn) {
                        return in_array($item['id'], $differenceIn);
                    });
                    $newInPlayers = array_values($newInPlayers);
                    if (count($newInPlayers) > 6) {
                        $newInPlayers = array();
                    }
                    // Get out players
                    $newOutPlayers = DB::table('players')
                        ->join('matchplayers', 'players.id', '=', 'matchplayers.playerid')
                        ->join('teams', 'players.team', '=', 'teams.id')
                        ->where('players.sport_type', $sportType->id)
                        ->where('matchplayers.sport_type', $sportType->id)
                        ->where('matchplayers.matchkey', strval($matchkey))
                        ->whereIn('matchplayers.playerid', $differenceOut)
                        ->select('players.id', 'players.team', 'matchplayers.name', 'teams.team as team_name')
                        ->get()->toArray();

                    $uniqueTeams = [];
                    // Iterate through the given array
                    foreach ($playerAndTeam as $item) {
                        if (!isset($uniqueTeams[$item['team_id']])) {
                            $uniqueTeams[$item['team_id']] = $item['team'];
                        }
                    }

                    $content = '';

                    // There are some out players in line up
                    if (count($differenceOut) > 0) {

                        // Create content for IN players
                        foreach ($uniqueTeams as $t_key => $t_row) {

                            $teamExist = array_values(array_unique(array_column($newInPlayers, 'team_id')));

                            if (in_array($t_key, $teamExist)) {
                                $content .= $t_row . ' : ';

                                foreach ($newInPlayers as $m_key => $row) {
                                    if ($t_key == $row['team_id']) {
                                        $content .= $row['name'] . ', ';
                                    }
                                }
                            }
                        }
                        $count = (count($newInPlayers) == 1) ? 'is' : 'are';
                        $content = trim($content, ', ');
                        $content .= ' ' . $count . ' IN, ';

                        // Create content for OUT players
                        foreach ($uniqueTeams as $t_key => $t_row) {

                            $teamExist = array_values(array_unique(array_column($newOutPlayers, 'team')));

                            if (in_array($t_key, $teamExist)) {
                                $content .= $t_row . ' : ';

                                foreach ($newOutPlayers as $m_key => $row) {
                                    if ($t_key == $row->team) {
                                        $content .= $row->name . ', ';
                                    }
                                }
                            }
                        }
                        $count = (count($newInPlayers) == 1) ? 'is' : 'are';
                        $content = trim($content, ', ');
                        $content .= ' ' . $count . ' OUT, ';

                        $content .= ' Check and update your team';
                    } else {

                        if (count($newInPlayers) > 0) {

                            if (count($newInPlayers) == 1) {
                                $content .= $newInPlayers[0]['name'] . ' (' . $newInPlayers[0]['team'] . ') is now available for selection. Check and update yours team!';
                            } else {

                                foreach ($uniqueTeams as $t_key => $t_row) {

                                    $teamExist = array_values(array_unique(array_column($newInPlayers, 'team_id')));

                                    if (in_array($t_key, $teamExist)) {
                                        $content .= $t_row . ' : ';

                                        foreach ($newInPlayers as $m_key => $row) {
                                            if ($t_key == $row['team_id']) {
                                                $content .= $row['name'] . ', ';
                                            }
                                        }
                                    }
                                }

                                $content = trim($content, ', ');
                                $content .= ' are now available for selection.';
                            }
                        }
                    }

                    if ($content != '') {
                        $updateAlert = ListMatchDetails::updateOrCreate(
                            [
                                'matchkey' => $matchkey,
                                'sport_type' => $sportType->id,
                            ],
                            [
                                'alert_notify_content' => $content
                            ]
                        );
                    }
                }

                $insert_data = array(
                    "match_key" => $matchkey,
                    "sport_type" => $sportType->id,
                    "player_ids" => serialize($players),
                );



                $matchPlaying11DB = DB::connection('mysql-write')->table('match_playing11')
                    ->where('match_key', $matchkey)->where('sport_type', $sportType->id)->first();
                if (empty($matchPlaying11DB)) {
                    DB::table('match_playing11')->insert($insert_data);
                    MatchesController::sendMatchAlertNotification($matchkey, $sportType->id);
                } else {
                    DB::table('match_playing11')->where('id', $matchPlaying11DB->id)->update($insert_data);
                }


                // if($sportType->id == 1) {
                try {
                    if ($sportType->id == 1) {
                        $br = MatchesController::updateBattingOrderOfPlayers($matchkey);
                    }
                } catch (Exception $e) {
                }


                return $players;
            } else {
                return array();
            }

           
        }

         
        return array();
    }
}
