<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use App\Services\CurlService;

/**
 * Fast Cricket Scorecard Controller - Optimized for Speed
 */
class FastSportsInteractiveController extends Controller
{
    /**
     * Maps skill values to player roles.
     */
    private const ROLE_BY_SKILL = [
        1 => 'batter',
        2 => 'bowler',
        3 => 'all-rounder',
        4 => 'wicketkeeper',
    ];

    /**
     * Main entry point - ultra-fast with caching
     */
    public function calculate(Request $request)
    {
        $apiKey = "ea9fc1ae0ce14b1394f7589bb2437878";
        if (empty($request->token) || $request->token != $apiKey) {
            return response()->json([
                'status'  => 'error',
                'message' => 'Invalid Token authentication',
                'code'    => 401
            ], 401);
        }

        $gameId = $request->query('game_id');
        if (!$gameId) {
            return response()->json(['status' => 'error', 'message' => 'game_id required'], 422);
        }

        // Optimized cache with language support
        $lang = $request->query('lang', 'en');
        $cacheKey = "fast_scorecard_{$gameId}_{$lang}";

        if (cache()->has($cacheKey)) {
            $payload = cache()->get($cacheKey);
        } else {
            $payload = $this->fetchScorecard($gameId, $lang, 'json');

            if ($payload && !empty($payload['Matchdetail'])) {
                // Cache for 60 seconds for better performance
                cache()->put($cacheKey, $payload, 60);
            }
        }

        if (!$payload || empty($payload['Matchdetail'])) {
            return response()->json([
                'status'  => 'error',
                'message' => 'No game found',
                'code'    => 404
            ], 404);
        }

        $format = strtoupper(Arr::get($payload, 'Matchdetail.Match.Type', 'T20'));
        $md = $payload['Matchdetail'] ?? [];
        $innings = $payload['Innings'] ?? [];
        $teams = $payload['Teams'] ?? [];

        // Fast match info extraction
        $homeId = Arr::get($md, 'Team_Home');
        $awayId = Arr::get($md, 'Team_Away');

        $matchInfo = [
            'venue'  => Arr::get($md, 'Venue.Name'),
            'status' => Arr::get($md, 'Status', ''), // Match status
            'display_status' => Arr::get($md, 'Match_display_status', ''), // Match status
            'team_home' => [
                'id'    => (int)$homeId,
                'name'  => Arr::get($teams, "$homeId.Name_Full"),
                'short' => Arr::get($teams, "$homeId.Name_Short"),
            ],
            'team_away' => [
                'id'    => (int)$awayId,
                'name'  => Arr::get($teams, "$awayId.Name_Full"),
                'short' => Arr::get($teams, "$awayId.Name_Short"),
            ],
        ];

        // Ultra-fast data processing - direct API data usage
        $battingIndex = [];
        $bowlingIndex = [];

        // Build indexes once - use API data directly, no calculations
        foreach ($innings as $inning) {
            foreach ($inning['Batsmen'] ?? [] as $batsman) {
                $pid = $batsman['Batsman'];
                $battingIndex[$pid] = [
                    'runs' => $batsman['Runs'] ?? 0,
                    'balls' => $batsman['Balls'] ?? 0,
                    'fours' => $batsman['Fours'] ?? 0,
                    'sixes' => $batsman['Sixes'] ?? 0,
                    'strike_rate' => $batsman['Strikerate'] ?? 0, // Direct from API
                    'dismissal' => $batsman['Dismissal'] ?? 'not out',
                    'howout' => $batsman['Howout'] ?? '', // Full dismissal text
                    'howout_short' => $batsman['Howout_short'] ?? '', // Short dismissal text
                    'position' => $batsman['Number'] ?? 0
                ];
            }

            foreach ($inning['Bowlers'] ?? [] as $bowler) {
                $pid = $bowler['Bowler'];
                $bowlingIndex[$pid] = [
                    'overs' => $bowler['Overs'] ?? 0, // Direct string from API
                    'maidens' => $bowler['Maidens'] ?? 0,
                    'runs_conceded' => $bowler['Runs'] ?? 0,
                    'wickets' => $bowler['Wickets'] ?? 0,
                    'economy' => $bowler['Economyrate'] ?? 0 // Direct from API
                ];
            }
        }

        $teamWisePlayers = [];

        foreach ($teams as $teamId => $teamData) {
            $teamPlayers = [];

            foreach ($teamData['Players'] ?? [] as $pid => $pData) {
                $batting = $battingIndex[$pid] ?? ['runs' => 0, 'balls' => 0, 'fours' => 0, 'sixes' => 0, 'strike_rate' => 0, 'dismissal' => 'not out', 'howout' => '', 'howout_short' => '', 'position' => 0];
                $bowling = $bowlingIndex[$pid] ?? ['overs' => 0, 'maidens' => 0, 'runs_conceded' => 0, 'wickets' => 0, 'economy' => 0];

                $teamPlayers[] = [
                    'player_id'    => $pid,
                    'player_name'  => $pData['Name_Full'] ?? '',
                    'role'         => self::ROLE_BY_SKILL[$pData['Skill']] ?? 'batter',
                    'team_id'      => $teamId,
                    'team_short'   => $teamData['Name_Short'] ?? '',
                    'total_points' => 0,
                    'runs'         => $batting['runs'],
                    'balls'        => $batting['balls'],
                    'fours'        => $batting['fours'],
                    'sixes'        => $batting['sixes'],
                    'strike_rate'  => $batting['strike_rate'], // Direct from API
                    'dismissal'    => $batting['dismissal'],
                    'howout'       => $batting['howout'], // Full dismissal text
                    'howout_short' => $batting['howout_short'], // Short dismissal text
                    'batting_position' => $batting['position'],
                    'overs'        => $bowling['overs'], // Direct string from API
                    'maidens'      => $bowling['maidens'],
                    'runs_conceded' => $bowling['runs_conceded'],
                    'wickets'      => $bowling['wickets'],
                    'economy'      => $bowling['economy'], // Direct from API
                    'playing_xi'   => !empty($pData['Confirm_XI']),
                    'breakup'      => [],
                ];
            }

            $teamWisePlayers[$teamData['Name_Short']] = [
                'team_name' => $teamData['Name_Full'] ?? $teamData['Name_Short'],
                'team_short' => $teamData['Name_Short'],
                'team_id' => (int) $teamId,
                'players' => $teamPlayers,
                'score' => $this->getTeamScore($teamId, $innings)
            ];
        }

        if ($request->response == 'json') {
            return response()->json([
                'status'     => 'success',
                'calculated' => date('Y-m-d H:i:s'),
                'game_id'    => $gameId,
                'format'     => $format,
                'match_details' => $matchInfo,
                'team_wise_players' => array_values($teamWisePlayers),
            ]);
        }

        // Generate response with compression headers
        $response = response()->view('admin.match_player_scorecard', [
            'gameId' => $gameId,
            'format' => $format,
            'matchInfo' => $matchInfo,
            'teamWisePlayers' => $teamWisePlayers,
            'allPlayers' => []
        ]);

        // Add performance headers

        // Cache the response publicly (browser, proxy, CDN) for 30 seconds
        // Cache-Control Header:
        // 'public' => Response ko browser, proxy, CDN sabhi cache kar sakte hain
        // 'max-age=30' => Response 30 seconds tak cache me valid rahega.
        // Is duration me agar same request aaye to cache se serve hoga, server dobara hit nahi hoga.
        $response->header('Cache-Control', 'public, max-age=30');

        // Cache separate versions based on the client's accepted encoding (gzip, br, etc.)
        // Vary Header:
        // 'Accept-Encoding' => Different encoding (gzip, br, deflate) ke basis par cache ka alag version banega.
        // Matlab agar client ne gzip bola to gzip wala version cache hoga, br bola to br wala.
        $response->header('Vary', 'Accept-Encoding');

        return $response;
    }



    /**
     * Fast team score extraction - direct API values
     */
    private function getTeamScore($teamId, $innings)
    {
        foreach ($innings as $inning) {
            if ($inning['Battingteam'] == $teamId) {
                return [
                    'runs' => $inning['Total'],
                    'wickets' => $inning['Wickets'],
                    'overs' => $inning['Overs'],
                    'extras' => [
                        'byes' => $inning['Byes'] ?? 0,
                        'legbyes' => $inning['Legbyes'] ?? 0,
                        'wides' => $inning['Wides'] ?? 0,
                        'noballs' => $inning['Noballs'] ?? 0,
                        'penalty' => $inning['Penalty'] ?? 0,
                    ]
                ];
            }
        }
        return null;
    }

    /**
     * Fetch match scorecard data from Sportz.io
     */
    private function fetchScorecard(string $gid, string $lang, string $fmt)
    {
        $cid = env('SPORTZ_CLIENT_ID', 'c473b3f16d35');
        $url = "https://assets-vision11.sportz.io/cricket/v1/game/scorecard?"
            . "game_id={$gid}&lang={$lang}&feed_format={$fmt}&client_id={$cid}";

        $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);
        $data = json_decode($json, true);

        if (json_last_error()) {
            throw new \Exception('JSON error: ' . json_last_error_msg());
        }
        return $data['data'] ?? [];
    }
}
