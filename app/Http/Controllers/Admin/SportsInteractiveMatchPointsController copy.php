<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use App\Services\CurlService;
use phpDocumentor\Reflection\Types\Float_;

/**
 * Vision11 – Sports-Interactive scorecard to fantasy points calculator.
 *
 * GET /get-sport-interactive-scorecard?game_id=⟨code⟩&lang=en&feed_format=json
 *
 * - Fetches scorecard data from Sportz.io
 * - Applies fantasy point rules (defined in this controller)
 * - Returns per-player stats with detailed category breakdown
 */

class SportsInteractiveMatchPointsController extends Controller
{
    /* --------------------------------------------------------- *
     |  STATIC   CONFIGURATION  TABLES                           |
     * --------------------------------------------------------- */

    /**
     * Maps skill values to player roles.
     */
    private const ROLE_BY_SKILL = [
        1 => 'batter',
        2 => 'bowler',
        3 => 'all-rounder',
        4 => 'wicketkeeper',
    ];

    /**
     * Normal points table by match format.
     */
    private const NORMAL = [
        'T20'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -2,   // batter/wk/all-rounder
        ],
        'ODI'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -3,
        ],
        'TEST' => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 16,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -4,
        ],
        'T10'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -2,
        ],
    ];

    /* ----------  BONUS  POINTS  ---------- */
    private const BONUS = [
        'T20' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  4,
            'half_century'   =>  8,
            'century'        => 16,
            'maiden_over'    => 12,
            'lbw_bowled'     =>  8,
            'wickets_3'      =>  4,
            'wickets_4'      =>  8,
            'wickets_5'      => 16,
        ],
        'ODI' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  0,   // N/A
            'half_century'   =>  4,
            'century'        =>  8,
            'maiden_over'    =>  4,
            'lbw_bowled'     =>  8,
            'wickets_4'      =>  4,
            'wickets_5'      =>  8,
        ],
        'TEST' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  0,
            'half_century'   =>  4,
            'century'        =>  8,
            'maiden_over'    =>  0,
            'lbw_bowled'     =>  8,
            'wickets_4'      =>  4,
            'wickets_5'      =>  8,
        ],
        'T10' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  8,
            'half_century'   => 16,
            'century'        =>  0,
            'maiden_over'    => 16,
            'lbw_bowled'     =>  8,
            'wickets_2'      =>  8,
            'wickets_3'      => 16,
        ],
    ];

    /* ----------  ECONOMY-RATE  ---------- */
    private const ECONOMY = [
        'T20' => [
            'min_overs' => 2,
            'rules' => [
                [0,     4.99,   6],
                [5.0,   5.99,   4],
                [6.0,   7.00,   2],
                [10.0, 10.99,  -2],
                [11.0, 11.99,  -4],
                [12.0,  null,  -6],   // 12+
            ],
        ],
        'ODI' => [
            'min_overs' => 5,
            'rules' => [
                [0,    2.49,   6],
                [2.5,  3.49,   4],
                [3.5,  4.5,    2],
                [7.0,  7.99,  -2],
                [8.0,  8.99,  -4],
                [9.0,  null,  -6],
            ],
        ],
        'T10' => [
            'min_overs' => 1,
            'rules' => [
                [0,    6.99,   6],
                [7.0,  7.99,   4],
                [8.0,  8.99,   2],
                [14.0, 14.99, -2],
                [15.1, 15.99, -4],
                [16.0, null,  -6],
            ],
        ],
    ];

    /* ----------  STRIKE-RATE  ---------- */
    private const STRIKE_RATE = [
        'T20' => [
            'min_balls' => 10,
            'rules' => [
                [0,    49.99, -6],
                [50.0, 59.99, -4],
                [60.0, 70.0,  -2],
                [130.0, 149.99,  2],
                [150.0, 170.0,   4],
                [170.01, null,   6],
            ],
        ],
        'ODI' => [
            'min_balls' => 20,
            'rules' => [
                [0,   29.99, -6],
                [30.0, 39.99, -4],
                [40.0, 50.0,  -2],
                [100.0, 119.99, 2],
                [120.01, 140.0, 4],
                [140.01, null,  6],
            ],
        ],
        'T10' => [
            'min_balls' => 5,
            'rules' => [
                [0,   59.99, -6],
                [60.0, 69.99, -4],
                [70.0, 80.0,  -2],
                [150.1, 170.0, 2],
                [170.1, 190.0, 4],
                [190.01, null, 6],
            ],
        ],
    ];

    /* ================================================================ */
    /* =======================   ENTRY-POINT   ======================== */
    /**
     * Main entry point for calculating player points.
     */
    /* ================================================================ */
    public function calculate(Request $request)
    {
        $gameId = $request->query('game_id');
        if (!$gameId) {
            return response()->json(['status' => 'error', 'message' => 'game_id required'], 422);
        }

        $payload = $this->fetchScorecard(
            $gameId,
            $request->query('lang', 'en'),
            $request->query('feed_format', 'json')
        );

        if (!$payload || empty($payload['Matchdetail'])) {
            // Nothing came back for this game_id ⇒ bail out early
            return response()->json([
                'status'  => 'error',
                'message' => 'No game found',
                'code'    => 404
            ], 404);
        }

        $format = strtoupper(Arr::get($payload, 'Matchdetail.Match.Type', 'T20'));
        if (!isset(self::NORMAL[$format])) {
            return response()->json(['status' => 'error', 'message' => 'Unsupported format'], 422);
        }

        /* ---------- Extract basic match / series information ---------- */
        $md   = $payload['Matchdetail'] ?? [];
        $Innings   = $payload['Innings'] ?? [];
        $teams = $payload['Teams'] ?? [];

        $homeId = Arr::get($md, 'Team_Home');
        $awayId = Arr::get($md, 'Team_Away');

        $matchInfo = [
            'match_status'   => Arr::get($md, 'Status'),
            'match_status_id'   => Arr::get($md, 'Status_Id'),
            'code'   => Arr::get($md, 'Match.Code'),
            'date'   => Arr::get($md, 'Match.Date'),
            'time'   => Arr::get($md, 'Match.Time'),
            'venue'  => Arr::get($md, 'Venue.Name'),
            'innings' => isset($payload['Innings'])?$payload['Innings']:[],
            'series' => [
                'id'    => Arr::get($md, 'Series.Id'),
                'name'    => Arr::get($md, 'Series.Name'),
                'series_type'    => Arr::get($md, 'Series.Series_type'),
                'tour_id'    => Arr::get($md, 'Series.Tour'),
                'tour_name'    => Arr::get($md, 'Series.Tour_Name'),
                'start_date'  =>  Arr::get($md, 'Series.Series_start_date'),
                'end_date'  => Arr::get($md, 'Series.Series_end_date')
            ],
            'team_home' => [
                'id'    => (int)$homeId,
                'name'  => Arr::get($teams, "$homeId.Name_Full"),
                'short' => Arr::get($teams, "$homeId.Name_Short"),
            ],
            'team_away' => [
                'id'    => (int)$awayId,
                'name'  => Arr::get($teams, "$awayId.Name_Full"),
                'short' => Arr::get($teams, "$awayId.Name_Short"),
            ],
        ];

        $homeInning = collect($Innings)->first(function ($inn) use ($homeId) {
            return (string)$inn['Battingteam'] === (string)$homeId || (string)$inn['Bowlingteam'] === (string)$homeId;
        });

        $awayInning = collect($Innings)->first(function ($inn) use ($awayId) {
            return (string)$inn['Battingteam'] === (string)$awayId || (string)$inn['Bowlingteam'] === (string)$awayId;
        });

        $matchInfo['team_home']['score'] = $homeInning ? [
            'runs'    => (int)($homeInning['Total'] ?? 0),
            'wickets' => (int)($homeInning['Wickets'] ?? 0),
            'overs'   => $this->oversToFloat((string)($homeInning['Overs'] ?? '0.0')),
            'score'   => "{$homeInning['Total']}/{$homeInning['Wickets']} ({$homeInning['Overs']} ov)",
        ] : null;

        $matchInfo['team_away']['score'] = $awayInning ? [
            'runs'    => (int)($awayInning['Total'] ?? 0),
            'wickets' => (int)($awayInning['Wickets'] ?? 0),
            'overs'   => $this->oversToFloat((string)($awayInning['Overs'] ?? '0.0')),
            'score'   => "{$awayInning['Total']}/{$awayInning['Wickets']} ({$awayInning['Overs']} ov)",
        ] : null;

        /* ===========  MASTER STORES  =========== */
        $totals = [];              // pid => total pts
        $breaks = [];              // pid => [cat => [count, per_point, points]]
        $roles  = [];              // pid => role
        $names  = [];             // ► pid → full player name

        /* ---- Starting XI ---- */
        foreach (Arr::get($payload, 'Teams', []) as $team) {
            foreach ($team['Players'] ?? [] as $pid => $pData) {
                $roles[$pid] = self::ROLE_BY_SKILL[$pData['Skill']] ?? 'batter';
                $names[$pid] = $pData['Name_Full'] ?? '';
                if (!empty($pData['Confirm_XI'])) {
                    $this->addStat($pid, 'starting_xi', 1, self::NORMAL[$format]['starting_xi'], $totals, $breaks);
                }
            }
        }

        /* ---- Innings + Super-over ---- */
        $allInnings = array_merge(
            Arr::get($payload, 'Innings', []),
            Arr::get($payload, 'Superover.Innings', [])
        );

        foreach ($allInnings as $inn) {
            $this->processBatting($inn, $format, $totals, $breaks, $roles);
            $this->processBowling($inn, $format, $totals, $breaks);
            $this->processFielding($inn, $format, $totals, $breaks);
            $this->processDismissalBonus($inn, $format, $totals, $breaks);
        }

        /* ----  RESPONSE  ---- */
        $players = [];
        $rows = [];
        foreach ($totals as $pid => $pts) {

            $bk = $breaks[$pid] ?? [];
            $team = $teamShort[$pid] ?? '';

            // $cnt  = static function (array $bk, string $k): int {
            //     return $bk[$k]['count'] ?? 0;
            // };
            $cnt = static function (array $bk, string $k) {
                return $bk[$k]['count'] ?? 0;
            };


            $pts_ = static function (array $bk, string $k): int {
                return $bk[$k]['points'] ?? 0;
            };
            $runOut = $cnt($bk, 'run_out_thrower') + $cnt($bk, 'run_out_catcher');

            $players[] = $player = [
                'player_id'    => (int) $pid,
                'player_name'  => $names[$pid]   ?? '',
                'role'         => $roles[$pid]   ?? 'batter',
                'team_short'   => $team,
                'total_points' => $pts,
                'breakup'      => $bk,
            ];

            $playerInningMap = []; // [player_id => ['inning_number' => 1, 'team_id' => '1791']]

            foreach ($Innings as $index => $inn) {
                $inningNumber = $index + 1;

                foreach ($inn['Batsmen'] ?? [] as $batsman) {
                    $plrid = $batsman['Batsman'] ?? null;
                    if ($plrid) {
                        $playerInningMap[$plrid] = [
                            'inning_number' => $inningNumber,
                            'team_id'       => $inn['Battingteam'] ?? null,
                        ];
                    }
                }

                foreach ($inn['Bowlers'] ?? [] as $bowler) {
                    $plrid = $bowler['Bowler'] ?? null;
                    if ($plrid) {
                        $playerInningMap[$plrid] = [
                            'inning_number' => $inningNumber,
                            'team_id'       => $inn['Bowlingteam'] ?? null,
                        ];
                    }
                }
            }

            $playerTeamId = $playerInningMap[$pid]['team_id'] ?? null;
            $playerInning = $playerInningMap[$pid]['inning_number'] ?? null;
            $rows[] = [
                'team_id'       => $playerTeamId,
                'inning_number' => $playerInning,
                'team_name'     => $team,
                'player_key'    => $pid,
                'player_name'   => $player['player_name'],
                'player_role'   => $player['role'],
                'start_point'   => $pts_($bk, 'starting_xi'),
                'run'           => $pts_($bk, 'runs'),
                'balls' => $cnt($bk, 'balls'),
                'fours'         => $pts_($bk, 'boundaries'),
                'fours_count'   => $cnt($bk, 'boundaries'),
                'sixs'          => $pts_($bk, 'sixes'),
                'sixs_count'   => $cnt($bk, 'sixes'),
                'strike_rate'   => $pts_($bk, 'strike_rate'),
                // 'strike_rate_per'   => $cnt($bk, 'strike_rate'),
                'century'       => $cnt($bk, 'century')      ? $pts_($bk, 'century')      : 0,
                'century_count'   => $cnt($bk, 'century'),
                'thirty_runs'   => $cnt($bk, 'run_bonus_30') ? $pts_($bk, 'run_bonus_30') : 0,
                'thirty_runs_count'   => $cnt($bk, 'run_bonus_30'),

                'wickets'       => $pts_($bk, 'wickets'),
                'wickets_count'   => $cnt($bk, 'wickets'),
                'maidens'       => $pts_($bk, 'maiden_over'),

                'maidens_count'   => $cnt($bk, 'maidens'),
                'balls_bowled'   => $cnt($bk, 'balls_bowled'),
                'bowler_runs_conceded'   => $cnt($bk, 'bowler_runs_conceded'), //economy_rate_per
                // 'economy_rate_per'   => $cnt($bk, 'economy_rate_per'),
                'economy_rate_per' => number_format($cnt($bk, 'economy_rate_per'), 2, '.', ''),
                //strike_rate_per
                'strike_rate_per' => number_format($cnt($bk, 'strike_rate_per'), 2, '.', ''),

                'overs'   => $cnt($bk, 'overs'),
                'no_ball'   => $cnt($bk, 'no_ball'),
                'wides'   => $cnt($bk, 'wides'),
                'dots'   => $cnt($bk, 'dots'),

                'economy_rate'  => $pts_($bk, 'economy_rate'),
                'economy_rate_count'   => $cnt($bk, 'economy_rate'),

                'catch'         => $pts_($bk, 'catches'),
                'catch_count'   => $cnt($bk, 'catches'),

                'run_outs'      => $runOut * self::NORMAL[$format]['run_out'],
                'run_outs_count'   => $cnt($bk, 'run_out'),

                'stumped'       => $pts_($bk, 'stumping'),
                'stumped_count'   => $cnt($bk, 'stumping'),


                'lbw_bonus'     => $pts_($bk, 'lbw_bowled'),
                'lbw_bowled_count'   => $cnt($bk, 'lbw_bowled'),

                'total_bonus'   => $pts,
            ];
        }

        return response()->json([
            'status'     => 'success',
            'calculated' => date('Y-m-d H:i:s'),
            'game_id'    => $gameId,
            'format'     => $format,
            'match_details'      => $matchInfo,
            'points'     => $players,
            'match_player_points' => $rows

        ]);
    }

    /* ================================================================ */
    /* =====================   FETCH SCORECARD   ====================== */
    /**
     * Fetch match scorecard data from Sportz.io
     */
    /* ================================================================ */
    private function fetchScorecard(string $gid, string $lang, string $fmt)
    {
        $cid = env('SPORTZ_CLIENT_ID', 'c473b3f16d35');
        $url = "https://assets-vision11.sportz.io/cricket/v1/game/scorecard?"
            . "game_id={$gid}&lang={$lang}&feed_format={$fmt}&client_id={$cid}";

        $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);
        $data = json_decode($json, true);

        if (json_last_error()) {
            throw new \Exception('JSON error: ' . json_last_error_msg());
        }
        return $data['data'] ?? [];
    }

    /* ================================================================ */
    /* ======================   BAT T I N G   ========================= */
    /**
     * Process batting-related stats and calculate respective points.
     */
    /* ================================================================ */
    private function processBatting(array $inn, string $fmt, &$tot, &$brk, &$roles)
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $pid   = $b['Batsman'];
            $runs  = (int)$b['Runs'];
            // $balls = max(1, (int)$b['Balls']);
            // $balls = max(1, (int)($b['Balls'] ?? 0));
            $fours = (int)$b['Fours'];
            $sixes = (int)$b['Sixes'];
            $balls = (int)($b['Balls'] ?? 0);  // Don't use max(1, …) if you want true value
            $this->addStat($pid, 'balls', $balls, 1, $tot, $brk);

            //strike Rate 
            $strike_rate_per = (float)  $b['Strikerate'];
            $this->addStat($pid, 'strike_rate_per', $strike_rate_per, 1, $tot, $brk);


            /* runs + boundaries + sixes */
            $this->addStat($pid, 'runs',      $runs,  self::NORMAL[$fmt]['run'],          $tot, $brk);
            $this->addStat($pid, 'boundaries', $fours, self::BONUS[$fmt]['boundary'] ?? 0,  $tot, $brk);
            $this->addStat($pid, 'sixes',     $sixes, self::BONUS[$fmt]['six'] ?? 0,       $tot, $brk);

            /* run-milestone bonus */

            if ($runs >= 100 && ($p = self::BONUS[$fmt]['century'] ?? 0)) {
                $this->addStat($pid, 'century', 1, $p, $tot, $brk);
            } elseif ($runs >= 50 && ($p = self::BONUS[$fmt]['half_century'] ?? 0)) {
                $this->addStat($pid, 'half_century', 1, $p, $tot, $brk);
            } elseif ($runs >= 30 && ($p = self::BONUS[$fmt]['run_bonus_30'] ?? 0)) {
                $this->addStat($pid, 'run_bonus_30', 1, $p, $tot, $brk);
            }

            /* strike-rate */
            $roleAllowed = in_array(
                $roles[$pid] ?? 'batter',
                ['batter', 'wicketkeeper', 'all-rounder']
            );

            $srMin = self::STRIKE_RATE[$fmt]['min_balls'] ?? 9999;
            // if ($balls >= $srMin) {
            if ($roleAllowed && $balls >= $srMin) {
                $sr = ($runs * 100) / $balls;
                foreach (self::STRIKE_RATE[$fmt]['rules'] as [$min, $max, $pts]) {
                    $inRange = $sr >= $min && (is_null($max) || $sr <= $max);
                    if ($inRange && $pts != 0) {
                        $this->addStat($pid, 'strike_rate', 1, $pts, $tot, $brk);
                        break;
                    }
                }
            }

            /* duck */
            $dismiss = strtolower(Arr::get($b, 'DismissalType', ''));
            if (
                $runs == 0 && $dismiss !== 'not out'
                && in_array($roles[$pid] ?? 'batter', ['batter', 'wicketkeeper', 'all-rounder'])
            ) {
                $this->addStat($pid, 'duck', 1, self::NORMAL[$fmt]['duck'], $tot, $brk);
            }
        }
    }

    /* ================================================================ */
    /* ======================   B O W L I N G   ======================= */
    /**
     * Process bowling-related stats and calculate respective points.
     */
    /* ================================================================ */
    private function processBowling(array $inn, string $fmt, &$tot, &$brk)
    {
        foreach ($inn['Bowlers'] ?? [] as $bw) {
            $pid     = $bw['Bowler'];
            // $overs   = (float)$bw['Overs'];
            $overs = $this->oversToFloat($bw['Overs']);
            $runsCon = (int)  $bw['Runs'];
            $wks     = (int)  $bw['Wickets'];
            $maidens = (int)  $bw['Maidens'];
            $balls_Bowled = (int)  $bw['Balls_Bowled'];


            // bowler_runs_conceded
            $bowler_runs_conceded = (int)  $bw['Runs'];
            $this->addStat($pid, 'bowler_runs_conceded', $bowler_runs_conceded, 1, $tot, $brk);

            //Economy Rate 
            $economy_rate_per = (float)  $bw['Economyrate'];
            $this->addStat($pid, 'economy_rate_per', $economy_rate_per, 1, $tot, $brk);

            

            //overs 
            $this->addStat($pid, 'overs', $overs, 1, $tot, $brk);

            // no ball 
            $no_ball = (int)  $bw['Noballs'];
            $this->addStat($pid, 'no_ball', $no_ball, 1, $tot, $brk);

            // wide ball 
            $wides = (int)  $bw['Wides'];
            $this->addStat($pid, 'wides', $wides, 1, $tot, $brk);

            //Dots 
            $dots = (int)  $bw['Dots'];
            $this->addStat($pid, 'dots', $dots, 1, $tot, $brk);

            /* wickets */
            $this->addStat($pid, 'wickets', $wks, self::NORMAL[$fmt]['wicket'], $tot, $brk);
            $this->addStat($pid, 'balls_bowled', $balls_Bowled, 1, $tot, $brk);


            /* wicket-bonus */
            if ($wks == 2 && ($p = self::BONUS[$fmt]['wickets_2'] ?? 0))
                $this->addStat($pid, 'wickets_2', 1, $p, $tot, $brk);
            elseif ($wks == 3 && ($p = self::BONUS[$fmt]['wickets_3'] ?? 0))
                $this->addStat($pid, 'wickets_3', 1, $p, $tot, $brk);
            elseif ($wks == 4 && ($p = self::BONUS[$fmt]['wickets_4'] ?? 0))
                $this->addStat($pid, 'wickets_4', 1, $p, $tot, $brk);
            elseif ($wks >= 5 && ($p = self::BONUS[$fmt]['wickets_5'] ?? 0))
                $this->addStat($pid, 'wickets_5', 1, $p, $tot, $brk);

            /* maiden overs */
            if ($maidens && ($p = self::BONUS[$fmt]['maiden_over'] ?? 0))
                $this->addStat($pid, 'maiden_over', $maidens, $p, $tot, $brk);

            /* economy */
            if (isset(self::ECONOMY[$fmt])) {
                $minOv = self::ECONOMY[$fmt]['min_overs'];
                if ($overs >= $minOv && $overs > 0) {
                    $eco = $runsCon / $overs;
                    foreach (self::ECONOMY[$fmt]['rules'] as [$min, $max, $pts]) {
                        $inRange = $eco >= $min && (is_null($max) || $eco <= $max);
                        if ($inRange && $pts != 0) {
                            $this->addStat($pid, 'economy_rate', 1, $pts, $tot, $brk);
                            break;
                        }
                    }
                }
            }
        }
    }

    /* ================================================================ */
    /* =======================   F I E L D I N G   ==================== */
    /**
     * Process fielding stats (catch, run-out, stumping) and assign points.
     */
    /* ================================================================ */
    private function processFielding(array $inn, string $fmt, &$tot, &$brk)
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $dismiss = strtolower(Arr::get($b, 'DismissalType', ''));
            $f1 = Arr::get($b, 'Fielder');
            $f2 = Arr::get($b, 'Fielder1') ?: $f1;

            /* catch */
            if ($dismiss === 'caught' && $f1) {
                $this->addStat($f1, 'catches', 1, self::NORMAL[$fmt]['catch'], $tot, $brk);
            }

            /* stumping */
            if (in_array($dismiss, ['st', 'stumped']) && $f1) {
                $this->addStat($f1, 'stumping', 1, self::NORMAL[$fmt]['stumping'], $tot, $brk);
            }

            /* run-out */
            // if (in_array($dismiss,['run out','ro'])) {
            //     if ($f1)
            //         $this->addStat($f1,'run_out_thrower',1,self::NORMAL[$fmt]['run_out'],$tot,$brk);
            //     if ($f2)
            //         $this->addStat($f2,'run_out_catcher',1,self::NORMAL[$fmt]['run_out'],$tot,$brk);
            // }
            if (in_array($dismiss, ['run out', 'ro'])) {
                $thrower = Arr::get($b, 'Fielder');
                $assist1 = Arr::get($b, 'Fielder1');
                $assist2 = Arr::get($b, 'Fielder2');


                if ($thrower) {
                    $this->addStat(
                        $thrower,
                        'run_out_thrower',
                        1,
                        self::NORMAL[$fmt]['run_out'],
                        $tot,
                        $brk
                    );
                }


                foreach (array_unique(array_filter([$assist1, $assist2])) as $fid) {
                    if ($fid !== $thrower) {
                        $this->addStat(
                            $fid,
                            'run_out_catcher',
                            1,
                            self::NORMAL[$fmt]['run_out'],
                            $tot,
                            $brk
                        );
                    }
                }
            }
        }
    }

    /* ================================================================ */
    /* ====================   LBW / BOWLED BONUS   ==================== */
    /**
     * Assign bonus points for LBW or Bowled dismissals.
     */
    /* ================================================================ */
    private function processDismissalBonus(array $inn, string $fmt, &$tot, &$brk)
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $type = strtolower(Arr::get($b, 'DismissalType', ''));
            // if (in_array($type,['b','lbw','lb'])) {
            if (in_array($type, ['b', 'bowled', 'lbw', 'lb'])) {
                $bowler = Arr::get($b, 'Bowler');
                if ($bowler && ($p = self::BONUS[$fmt]['lbw_bowled'] ?? 0)) {
                    $this->addStat($bowler, 'lbw_bowled', 1, $p, $tot, $brk);
                }
            }
        }
    }

    /* ================================================================ */
    /* ================   CENTRAL  ADD-STAT HELPER   ================== */
    /**
     * Utility to record stat count, per-point value, and total contribution.
     */
    /* ================================================================ */
    // private function addStat($pid, $cat, $cnt, $per, &$tot, &$brk)
    // {
    //     if (!$cnt || $per === 0) return;

    //     $pts = $cnt * $per;
    //     $tot[$pid] = ($tot[$pid] ?? 0) + $pts;

    //     if (!isset($brk[$pid][$cat])) {
    //         $brk[$pid][$cat] = ['count' => $cnt, 'per_point' => $per, 'points' => $pts];
    //     } else {
    //         $brk[$pid][$cat]['count']  += $cnt;
    //         $brk[$pid][$cat]['points'] += $pts;
    //     }
    // }
    private function addStat($pid, $cat, $cnt, $per, &$tot, &$brk)
    {
        // Skip if count is 0 or per-point is explicitly null
        if (!$cnt || $per === null) return;

        // Allow float values and round total points to 2 decimal places
        $pts = round($cnt * $per, 2);

        // Total points per player
        $tot[$pid] = round(($tot[$pid] ?? 0) + $pts, 2);

        // Breakup points by category
        if (!isset($brk[$pid][$cat])) {
            $brk[$pid][$cat] = [
                'count'     => $cnt,
                'per_point' => $per,
                'points'    => $pts
            ];
        } else {
            $brk[$pid][$cat]['count']  += $cnt;
            $brk[$pid][$cat]['points'] = round($brk[$pid][$cat]['points'] + $pts, 2);
        }
    }



    /* --------------------------------------------------------- *
 |  Convert "overs" string ("2.4", "15.1" …)  ➜  float       |
 |  Example: "2.4"  = 2 + 4/6  = 2.6667                     |
 * --------------------------------------------------------- */
    private function oversToFloat(string $ov): float
    {
        if (strpos($ov, '.') === false) {
            return (float) $ov;                   // "4"  ➜  4.0
        }

        [$o, $balls] = array_pad(explode('.', $ov, 2), 2, '0');
        return (int) $o + ((int) $balls) / 6;     // "2.4" ➜ 2.6667
    }
}
