<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use App\Services\CurlService;

/**
 * Vision11 – Sports-Interactive scorecard to fantasy points calculator.
 *
 * GET /get-sport-interactive-scorecard?game_id=⟨code⟩&lang=en&feed_format=json
 *
 * - Fetches scorecard data from Sportz.io
 * - Applies fantasy point rules (defined in this controller)
 * - Returns per-player stats with detailed category breakdown
 */

class SportsInteractiveMatchPlayerViewController extends Controller
{
    /* --------------------------------------------------------- *
     |  STATIC   CONFIGURATION  TABLES                           |
     * --------------------------------------------------------- */

    /**
     * Maps skill values to player roles.
     */
    private const ROLE_BY_SKILL = [
        1 => 'batter',
        2 => 'bowler',
        3 => 'all-rounder',
        4 => 'wicketkeeper',
    ];

    /**
     * Normal points table by match format.
     */
    private const NORMAL = [
        'T20'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -2,   // batter/wk/all-rounder
        ],
        'ODI'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -3,
        ],
        'TEST' => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 16,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -4,
        ],
        'T10'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -2,
        ],
    ];

    /* ----------  BONUS  POINTS  ---------- */
    private const BONUS = [
        'T20' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'half_century'   =>  8,
            'century'        => 16,
            'maiden_over'    => 12,
            'lbw_bowled'     =>  8,
        ],
        'ODI' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'half_century'   =>  4,
            'century'        =>  8,
            'maiden_over'    =>  4,
            'lbw_bowled'     =>  8,
        ],
        'TEST' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'half_century'   =>  4,
            'century'        =>  8,
            'lbw_bowled'     =>  8,
        ],
        'T10' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'half_century'   => 16,
            'maiden_over'    => 16,
            'lbw_bowled'     =>  8,
        ],
    ];



    /* ================================================================ */
    /* =======================   ENTRY-POINT   ======================== */
    /**
     * Main entry point for calculating player points.
     */
    /* ================================================================ */
    public function calculate(Request $request)
    {
        $apiKey = "ea9fc1ae0ce14b1394f7589bb2437878";
        if (empty($request->token) || $request->token != $apiKey) {
            return response()->json([
                'status'  => 'error',
                'message' => 'Invalid Token authentication',
                'code'    => 401
            ], 401);
        }
        $gameId = $request->query('game_id');
        if (!$gameId) {
            return response()->json(['status' => 'error', 'message' => 'game_id required'], 422);
        }

        $payload = $this->fetchScorecard(
            $gameId,
            $request->query('lang', 'en'),
            $request->query('feed_format', 'json')
        );

        if (!$payload || empty($payload['Matchdetail'])) {
            // Nothing came back for this game_id ⇒ bail out early
            return response()->json([
                'status'  => 'error',
                'message' => 'No game found',
                'code'    => 404
            ], 404);
        }

        $format = strtoupper(Arr::get($payload, 'Matchdetail.Match.Type', 'T20'));
        if (!isset(self::NORMAL[$format])) {
            return response()->json(['status' => 'error', 'message' => 'Unsupported format'], 422);
        }

        /* ---------- Extract basic match / series information ---------- */
        $md   = $payload['Matchdetail'] ?? [];
        $Innings   = $payload['Innings'] ?? [];
        $teams = $payload['Teams'] ?? [];

        $homeId = Arr::get($md, 'Team_Home');
        $awayId = Arr::get($md, 'Team_Away');

        $matchInfo = [
            'match_status'   => Arr::get($md, 'Status'),
            'match_status_id'   => Arr::get($md, 'Status_Id'),
            'code'   => Arr::get($md, 'Match.Code'),
            'date'   => Arr::get($md, 'Match.Date'),
            'time'   => Arr::get($md, 'Match.Time'),
            'venue'  => Arr::get($md, 'Venue.Name'),
            'innings' => isset($payload['Innings']) ? $payload['Innings'] : [],
            'series' => [
                'id'    => Arr::get($md, 'Series.Id'),
                'name'    => Arr::get($md, 'Series.Name'),
                'series_type'    => Arr::get($md, 'Series.Series_type'),
                'tour_id'    => Arr::get($md, 'Series.Tour'),
                'tour_name'    => Arr::get($md, 'Series.Tour_Name'),
                'start_date'  =>  Arr::get($md, 'Series.Series_start_date'),
                'end_date'  => Arr::get($md, 'Series.Series_end_date')
            ],
            'team_home' => [
                'id'    => (int)$homeId,
                'name'  => Arr::get($teams, "$homeId.Name_Full"),
                'short' => Arr::get($teams, "$homeId.Name_Short"),
            ],
            'team_away' => [
                'id'    => (int)$awayId,
                'name'  => Arr::get($teams, "$awayId.Name_Full"),
                'short' => Arr::get($teams, "$awayId.Name_Short"),
            ],
        ];

        $homeInning = collect($Innings)->first(function ($inn) use ($homeId) {
            return (string)$inn['Battingteam'] === (string)$homeId || (string)$inn['Bowlingteam'] === (string)$homeId;
        });

        $awayInning = collect($Innings)->first(function ($inn) use ($awayId) {
            return (string)$inn['Battingteam'] === (string)$awayId || (string)$inn['Bowlingteam'] === (string)$awayId;
        });

        $matchInfo['team_home']['score'] = $homeInning ? [
            'runs'    => (int)($homeInning['Total'] ?? 0),
            'wickets' => (int)($homeInning['Wickets'] ?? 0),
            'overs'   => $this->oversToFloat((string)($homeInning['Overs'] ?? '0.0')),
            'score'   => "{$homeInning['Total']}/{$homeInning['Wickets']} ({$homeInning['Overs']} ov)",
        ] : null;

        $matchInfo['team_away']['score'] = $awayInning ? [
            'runs'    => (int)($awayInning['Total'] ?? 0),
            'wickets' => (int)($awayInning['Wickets'] ?? 0),
            'overs'   => $this->oversToFloat((string)($awayInning['Overs'] ?? '0.0')),
            'score'   => "{$awayInning['Total']}/{$awayInning['Wickets']} ({$awayInning['Overs']} ov)",
        ] : null;

        /* ===========  MASTER STORES  =========== */
        $totals = [];              // pid => total pts
        $breaks = [];              // pid => [cat => [count, per_point, points]]
        $roles  = [];              // pid => role
        $names  = [];             // ► pid → full player name

        /* ---- Quick Processing for Display Only ---- */
        foreach (Arr::get($payload, 'Teams', []) as $team) {
            foreach ($team['Players'] ?? [] as $pid => $pData) {
                $roles[$pid] = self::ROLE_BY_SKILL[$pData['Skill']] ?? 'batter';
                $names[$pid] = $pData['Name_Full'] ?? '';
                // Skip complex point calculations for faster loading
                $totals[$pid] = 0;
                $breaks[$pid] = [];
            }
        }

        /* ---- Simplified Innings Processing ---- */
        $allInnings = Arr::get($payload, 'Innings', []);

        /* ----  RESPONSE  ---- */
        $teamWisePlayers = [];
        $allPlayers = [];



        // Process innings data to get actual match statistics
        $battingStats = [];
        $bowlingStats = [];

        foreach ($allInnings as $inning) {
            $battingTeamId = $inning['Battingteam'];
            $bowlingTeamId = $inning['Bowlingteam'];

            // Process batting stats from innings
            foreach ($inning['Batsmen'] ?? [] as $batsman) {
                $pid = $batsman['Batsman'];
                $battingStats[$pid] = [
                    'runs' => (int)($batsman['Runs'] ?? 0),
                    'balls' => (int)($batsman['Balls'] ?? 0),
                    'fours' => (int)($batsman['Fours'] ?? 0),
                    'sixes' => (int)($batsman['Sixes'] ?? 0),
                    'strike_rate' => (float)($batsman['Strikerate'] ?? 0),
                    'dismissal' => $batsman['Dismissal'] ?? 'not out',
                    'team_id' => $battingTeamId,
                    'batting_position' => (int)($batsman['Number'] ?? 0)
                ];
            }

            // Process bowling stats from innings
            foreach ($inning['Bowlers'] ?? [] as $bowler) {
                $pid = $bowler['Bowler'];
                $bowlingStats[$pid] = [
                    'overs' => $this->oversToFloat($bowler['Overs'] ?? '0'),
                    'maidens' => (int)($bowler['Maidens'] ?? 0),
                    'runs_conceded' => (int)($bowler['Runs'] ?? 0),
                    'wickets' => (int)($bowler['Wickets'] ?? 0),
                    'economy' => (float)($bowler['Economyrate'] ?? 0),
                    'team_id' => $bowlingTeamId
                ];
            }
        }

        // Process all players and organize by teams
        foreach ($teams as $teamId => $teamData) {
            $teamPlayers = [];

            foreach ($teamData['Players'] ?? [] as $pid => $pData) {
                $bk = $breaks[$pid] ?? [];
                $pts = $totals[$pid] ?? 0;
                $batting = $battingStats[$pid] ?? [];
                $bowling = $bowlingStats[$pid] ?? [];

                $player = [
                    'player_id'    => (int) $pid,
                    'player_name'  => $pData['Name_Full'] ?? '',
                    'role'         => $roles[$pid] ?? 'batter',
                    'team_id'      => (int) $teamId,
                    'team_short'   => $teamData['Name_Short'] ?? '',
                    'total_points' => $pts,
                    'runs'         => $batting['runs'] ?? 0,
                    'balls'        => $batting['balls'] ?? 0,
                    'fours'        => $batting['fours'] ?? 0,
                    'sixes'        => $batting['sixes'] ?? 0,
                    'strike_rate'  => $batting['strike_rate'] ?? 0,
                    'dismissal'    => $batting['dismissal'] ?? 'not out',
                    'batting_position' => $batting['batting_position'] ?? 0,
                    'overs'        => $bowling['overs'] ?? 0,
                    'maidens'      => $bowling['maidens'] ?? 0,
                    'runs_conceded' => $bowling['runs_conceded'] ?? 0,
                    'wickets'      => $bowling['wickets'] ?? 0,
                    'economy'      => $bowling['economy'] ?? 0,
                    'playing_xi'   => !empty($pData['Confirm_XI']),
                    'breakup'      => $bk,
                ];

                $teamPlayers[] = $player;
                $allPlayers[] = $player;
            }

            // Create team-wise structure with innings data
            $teamScore = null;
            foreach ($allInnings as $inning) {
                if ($inning['Battingteam'] == $teamId) {
                    $teamScore = [
                        'runs' => (int)$inning['Total'],
                        'wickets' => (int)$inning['Wickets'],
                        'overs' => $inning['Overs'],
                        'extras' => [
                            'byes' => (int)($inning['Byes'] ?? 0),
                            'legbyes' => (int)($inning['Legbyes'] ?? 0),
                            'wides' => (int)($inning['Wides'] ?? 0),
                            'noballs' => (int)($inning['Noballs'] ?? 0),
                            'penalty' => (int)($inning['Penalty'] ?? 0),
                        ]
                    ];
                    break;
                }
            }

            $teamWisePlayers[$teamData['Name_Short']] = [
                'team_name' => $teamData['Name_Full'] ?? $teamData['Name_Short'],
                'team_short' => $teamData['Name_Short'],
                'team_id' => (int) $teamId,
                'players' => $teamPlayers,
                'score' => $teamScore
            ];
        }

        if ($request->response == 'json') {
            return response()->json([
                'status'     => 'success',
                'calculated' => date('Y-m-d H:i:s'),
                'game_id'    => $gameId,
                'format'     => $format,
                'match_details' => $matchInfo,
                'team_wise_players' => array_values($teamWisePlayers),
                'all_players' => $allPlayers
            ]);
        }

        return view('admin.match_player_scorecard', [
            'gameId' => $gameId,
            'format' => $format,
            'matchInfo' => $matchInfo,
            'teamWisePlayers' => $teamWisePlayers,
            'allPlayers' => $allPlayers
        ]);
    }

    /* ================================================================ */
    /* =====================   FETCH SCORECARD   ====================== */
    /**
     * Fetch match scorecard data from Sportz.io
     */
    /* ================================================================ */
    private function fetchScorecard(string $gid, string $lang, string $fmt)
    {
        $cid = env('SPORTZ_CLIENT_ID', 'c473b3f16d35');
        $url = "https://assets-vision11.sportz.io/cricket/v1/game/scorecard?"
            . "game_id={$gid}&lang={$lang}&feed_format={$fmt}&client_id={$cid}";

        $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);
        $data = json_decode($json, true);

        if (json_last_error()) {
            throw new \Exception('JSON error: ' . json_last_error_msg());
        }
        return $data['data'] ?? [];
    }



    /* ================================================================ */
    /* ======================   B O W L I N G   ======================= */
    /**
     * Process bowling-related stats and calculate respective points.
     */
    /* ================================================================ */
    private function processBowling(array $inn, string $fmt, &$tot, &$brk)
    {
        foreach ($inn['Bowlers'] ?? [] as $bw) {
            $pid     = $bw['Bowler'];
            // $overs   = (float)$bw['Overs'];
            $overs = $this->oversToFloat($bw['Overs']);
            $runsCon = (int)  $bw['Runs'];
            $wks     = (int)  $bw['Wickets'];
            $maidens = (int)  $bw['Maidens'];
            $balls_Bowled = (int)  $bw['Balls_Bowled'];


            // bowler_runs_conceded
            $bowler_runs_conceded = (int)  $bw['Runs'];
            $this->addStat($pid, 'bowler_runs_conceded', $bowler_runs_conceded, 1, $tot, $brk);

            //Economy Rate 
            $economy_rate_per = (float)  $bw['Economyrate'];
            $this->addStat($pid, 'economy_rate_per', $economy_rate_per, 1, $tot, $brk);



            //overs 
            $this->addStat($pid, 'overs', $overs, 1, $tot, $brk);

            // no ball 
            $no_ball = (int)  $bw['Noballs'];
            $this->addStat($pid, 'no_ball', $no_ball, 1, $tot, $brk);

            // wide ball 
            $wides = (int)  $bw['Wides'];
            $this->addStat($pid, 'wides', $wides, 1, $tot, $brk);

            //Dots 
            $dots = (int)  $bw['Dots'];
            $this->addStat($pid, 'dots', $dots, 1, $tot, $brk);

            /* wickets */
            $this->addStat($pid, 'wickets', $wks, self::NORMAL[$fmt]['wicket'], $tot, $brk);
            $this->addStat($pid, 'balls_bowled', $balls_Bowled, 1, $tot, $brk);




            /* maiden overs */
            if ($maidens && ($p = self::BONUS[$fmt]['maiden_over'] ?? 0))
                $this->addStat($pid, 'maiden_over', $maidens, $p, $tot, $brk);

            /* economy rate calculation for display */
            if ($overs > 0) {
                $eco = $runsCon / $overs;
                $this->addStat($pid, 'economy_display', $eco, 1, $tot, $brk);
            }
        }
    }

    /* ================================================================ */
    /* =======================   F I E L D I N G   ==================== */
    /**
     * Process fielding stats (catch, run-out, stumping) and assign points.
     */
    /* ================================================================ */
    private function processFielding(array $inn, string $fmt, &$tot, &$brk)
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $dismiss = strtolower(Arr::get($b, 'DismissalType', ''));
            $f1 = Arr::get($b, 'Fielder');

            /* catch */
            if ($dismiss === 'caught' && $f1) {
                $this->addStat($f1, 'catches', 1, self::NORMAL[$fmt]['catch'], $tot, $brk);
            }

            /* stumping */
            if (in_array($dismiss, ['st', 'stumped']) && $f1) {
                $this->addStat($f1, 'stumping', 1, self::NORMAL[$fmt]['stumping'], $tot, $brk);
            }

            /* run-out */
            // if (in_array($dismiss,['run out','ro'])) {
            //     if ($f1)
            //         $this->addStat($f1,'run_out_thrower',1,self::NORMAL[$fmt]['run_out'],$tot,$brk);
            //     if ($f2)
            //         $this->addStat($f2,'run_out_catcher',1,self::NORMAL[$fmt]['run_out'],$tot,$brk);
            // }
            if (in_array($dismiss, ['run out', 'ro'])) {
                $thrower = Arr::get($b, 'Fielder');
                $assist1 = Arr::get($b, 'Fielder1');
                $assist2 = Arr::get($b, 'Fielder2');


                if ($thrower) {
                    $this->addStat(
                        $thrower,
                        'run_out_thrower',
                        1,
                        self::NORMAL[$fmt]['run_out'],
                        $tot,
                        $brk
                    );
                }


                foreach (array_unique(array_filter([$assist1, $assist2])) as $fid) {
                    if ($fid !== $thrower) {
                        $this->addStat(
                            $fid,
                            'run_out_catcher',
                            1,
                            self::NORMAL[$fmt]['run_out'],
                            $tot,
                            $brk
                        );
                    }
                }
            }
        }
    }

    /* ================================================================ */
    /* ====================   LBW / BOWLED BONUS   ==================== */
    /**
     * Assign bonus points for LBW or Bowled dismissals.
     */
    /* ================================================================ */
    private function processDismissalBonus(array $inn, string $fmt, &$tot, &$brk)
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $type = strtolower(Arr::get($b, 'DismissalType', ''));
            // if (in_array($type,['b','lbw','lb'])) {
            if (in_array($type, ['b', 'bowled', 'lbw', 'lb'])) {
                $bowler = Arr::get($b, 'Bowler');
                if ($bowler && ($p = self::BONUS[$fmt]['lbw_bowled'] ?? 0)) {
                    $this->addStat($bowler, 'lbw_bowled', 1, $p, $tot, $brk);
                }
            }
        }
    }

    /* ================================================================ */
    /* ================   CENTRAL  ADD-STAT HELPER   ================== */
    /**
     * Utility to record stat count, per-point value, and total contribution.
     */
    /* ================================================================ */
    // private function addStat($pid, $cat, $cnt, $per, &$tot, &$brk)
    // {
    //     if (!$cnt || $per === 0) return;

    //     $pts = $cnt * $per;
    //     $tot[$pid] = ($tot[$pid] ?? 0) + $pts;

    //     if (!isset($brk[$pid][$cat])) {
    //         $brk[$pid][$cat] = ['count' => $cnt, 'per_point' => $per, 'points' => $pts];
    //     } else {
    //         $brk[$pid][$cat]['count']  += $cnt;
    //         $brk[$pid][$cat]['points'] += $pts;
    //     }
    // }
    private function addStat($pid, $cat, $cnt, $per, &$tot, &$brk)
    {
        // Skip if count is 0 or per-point is explicitly null
        if (!$cnt || $per === null) return;

        // Allow float values and round total points to 2 decimal places
        $pts = round($cnt * $per, 2);

        // Total points per player
        $tot[$pid] = round(($tot[$pid] ?? 0) + $pts, 2);

        // Breakup points by category
        if (!isset($brk[$pid][$cat])) {
            $brk[$pid][$cat] = [
                'count'     => $cnt,
                'per_point' => $per,
                'points'    => $pts
            ];
        } else {
            $brk[$pid][$cat]['count']  += $cnt;
            $brk[$pid][$cat]['points'] = round($brk[$pid][$cat]['points'] + $pts, 2);
        }
    }



    /* --------------------------------------------------------- *
 |  Convert "overs" string ("2.4", "15.1" …)  ➜  float       |
 |  Example: "2.4"  = 2 + 4/6  = 2.6667                     |
 * --------------------------------------------------------- */
    private function oversToFloat(string $ov): float
    {
        if (strpos($ov, '.') === false) {
            return (float) $ov;                   // "4"  ➜  4.0
        }

        [$o, $balls] = array_pad(explode('.', $ov, 2), 2, '0');
        return (int) $o + ((int) $balls) / 6;     // "2.4" ➜ 2.6667
    }
}
