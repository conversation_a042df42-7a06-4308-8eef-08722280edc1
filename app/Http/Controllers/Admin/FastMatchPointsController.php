<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\PointsConfig;
use App\Services\ScorecardFetcher;
use App\Services\MatchInfoProcessor;
use App\Services\PlayerPointsProcessor;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class FastMatchPointsController extends Controller
{
    private const CACHE_TTL = 300; // 5 minutes
    
    /**
     * Fast API endpoint for match points with conditional response
     * 
     * Query Parameters:
     * - game_id (required): Match ID
     * - type (optional): 'points', 'count', 'all' (default: 'all')
     * - format (optional): Response format 'json' or 'view' (default: 'json')
     * - token (required): API token
     */
    public function calculate(Request $request)
    {
        // Validate token first (fastest validation)
        if (!$this->validateToken($request->token)) {
            return $this->errorResponse('Invalid token', 401);
        }

        // Validate required parameters
        $gameId = $request->query('game_id');
        if (!$gameId) {
            return $this->errorResponse('game_id is required', 422);
        }

        $type = strtolower($request->query('type', 'all'));
        if (!in_array($type, ['points', 'count', 'all'])) {
            return $this->errorResponse('type must be: points, count, or all', 422);
        }

        // Use aggressive caching for performance
        $cacheKey = "fast_match_points_{$gameId}_{$type}";
        
        $result = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($gameId, $type) {
            return $this->processMatchData($gameId, $type);
        });

        if (!$result) {
            return $this->errorResponse('No match data found', 404);
        }

        // Return JSON response with performance headers
        return Response::json($result)
            ->header('Cache-Control', 'public, max-age=' . self::CACHE_TTL)
            ->header('X-Response-Type', $type)
            ->header('X-Cache-Key', $cacheKey);
    }

    /**
     * Process match data and return filtered response
     */
    private function processMatchData(string $gameId, string $type): ?array
    {
        // Fetch scorecard data
        $payload = (new ScorecardFetcher())->getCached($gameId, 'en', self::CACHE_TTL);
        
        if (!$payload || empty($payload['Matchdetail'])) {
            return null;
        }

        $format = strtoupper($payload['Matchdetail']['Match']['Type'] ?? 'T20');
        
        if (!isset(PointsConfig::NORMAL[$format])) {
            return null;
        }

        // Process match info (lightweight)
        $matchInfo = (new MatchInfoProcessor())->process($payload);
        
        // Process player points
        $playerStats = (new PlayerPointsProcessor($format))->process($payload);

        // Filter response based on type parameter
        $filteredStats = $this->filterPlayerStats($playerStats, $type);

        return [
            'status' => 'success',
            'response_type' => $type,
            'calculated_at' => now()->toISOString(),
            'game_id' => $gameId,
            'format' => $format,
            'match_info' => [
                'status' => $matchInfo['match_status'] ?? 'unknown',
                'venue' => $matchInfo['venue'] ?? '',
                'teams' => [
                    'home' => $matchInfo['team_home']['name'] ?? '',
                    'away' => $matchInfo['team_away']['name'] ?? ''
                ]
            ],
            'player_stats' => $filteredStats,
            'total_players' => count($filteredStats)
        ];
    }

    /**
     * Filter player stats based on type parameter
     */
    private function filterPlayerStats(array $playerStats, string $type): array
    {
        if ($type === 'all') {
            return $playerStats; // Return complete data
        }

        $filtered = [];
        
        foreach ($playerStats as $player) {
            $baseData = [
                'player_key' => $player['player_key'],
                'player_name' => $player['player_name'],
                'team_name' => $player['team_name'],
                'player_role' => $player['player_role']
            ];

            if ($type === 'points') {
                // Only points data
                $filtered[] = array_merge($baseData, [
                    'total_points' => $player['total_bonus'],
                    'batting_points' => array_sum($player['batting_points']),
                    'bowling_points' => array_sum($player['bowling_points']),
                    'fielding_points' => array_sum($player['fielding_points']),
                    'bonus_points' => array_sum($player['bonus_points']),
                    'breakdown' => [
                        'runs' => $player['run'],
                        'boundaries' => $player['fours'],
                        'sixes' => $player['sixs'],
                        'wickets' => $player['wickets'],
                        'catches' => $player['catch'],
                        'starting_xi' => $player['start_point']
                    ]
                ]);
            } elseif ($type === 'count') {
                // Only count data
                $filtered[] = array_merge($baseData, [
                    'performance_counts' => [
                        'runs' => $player['run'],
                        'balls_faced' => $player['balls'],
                        'boundaries' => $player['fours_count'],
                        'sixes' => $player['sixs_count'],
                        'wickets' => $player['wickets_count'],
                        'maidens' => $player['maidens_count'],
                        'catches' => $player['catch_count'],
                        'run_outs' => $player['run_outs_count'],
                        'stumpings' => $player['stumped_count'],
                        'balls_bowled' => $player['balls_bowled'],
                        'overs_bowled' => $player['overs']
                    ],
                    'rates' => [
                        'strike_rate' => $player['strike_rate_per'],
                        'economy_rate' => $player['economy_rate_per']
                    ]
                ]);
            }
        }

        return $filtered;
    }

    /**
     * Validate API token
     */
    private function validateToken(?string $token): bool
    {
        return !empty($token) && $token === config('services.sportz_api.token');
    }

    /**
     * Return error response
     */
    private function errorResponse(string $message, int $code): \Illuminate\Http\JsonResponse
    {
        return Response::json([
            'status' => 'error',
            'message' => $message,
            'code' => $code,
            'timestamp' => now()->toISOString()
        ], $code);
    }

    /**
     * Get available match formats
     */
    public function formats(): \Illuminate\Http\JsonResponse
    {
        return Response::json([
            'status' => 'success',
            'available_formats' => array_keys(PointsConfig::NORMAL),
            'response_types' => ['points', 'count', 'all'],
            'cache_ttl' => self::CACHE_TTL
        ]);
    }

    /**
     * Clear cache for specific match
     */
    public function clearCache(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$this->validateToken($request->token)) {
            return $this->errorResponse('Invalid token', 401);
        }

        $gameId = $request->query('game_id');
        if (!$gameId) {
            return $this->errorResponse('game_id is required', 422);
        }

        $cleared = 0;
        foreach (['points', 'count', 'all'] as $type) {
            $cacheKey = "fast_match_points_{$gameId}_{$type}";
            if (Cache::forget($cacheKey)) {
                $cleared++;
            }
        }

        // Also clear scorecard cache
        (new ScorecardFetcher())->clearCache($gameId);

        return Response::json([
            'status' => 'success',
            'message' => "Cleared {$cleared} cache entries for game_id: {$gameId}",
            'game_id' => $gameId
        ]);
    }
}
