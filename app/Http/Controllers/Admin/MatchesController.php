<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\Cricket\CricketPointsService;

class MatchesController extends Controller
{
    /**
     * Calculate player points for a match
     *
     * @param string $matchKey
     * @param string $format
     * @param string $sport
     * @param int $fantasyType
     * @return array
     */
    public function player_point($matchKey, $format, $sport = "CRICKET", $fantasyType = 1)
    {
        try {
            // Get all players for this match
            $players = DB::table('result_matches')
                ->where('match_key', $matchKey)
                ->where('fantasy_type_id', $fantasyType)
                ->get();

            $playerPoints = [];

            foreach ($players as $player) {
                $totalPoints = 0;
                $pointsBreakdown = [];

                // Starting XI points
                if ($player->starting11) {
                    $startingPoints = 4; // Standard starting XI points
                    $totalPoints += $startingPoints;
                    $pointsBreakdown['starting_xi'] = $startingPoints;
                }

                // Batting points
                if ($player->runs > 0) {
                    $runPoints = $player->runs; // 1 point per run
                    $totalPoints += $runPoints;
                    $pointsBreakdown['runs'] = $runPoints;
                }

                // Boundary points
                if ($player->fours > 0) {
                    $boundaryPoints = CricketPointsService::calculateCricketBonusPoints($format, 'boundary', $player->fours);
                    $totalPoints += $boundaryPoints;
                    $pointsBreakdown['boundaries'] = $boundaryPoints;
                }

                // Six points
                if ($player->six > 0) {
                    $sixPoints = CricketPointsService::calculateCricketBonusPoints($format, 'six', $player->six);
                    $totalPoints += $sixPoints;
                    $pointsBreakdown['sixes'] = $sixPoints;
                }

                // Half century bonus
                if ($player->runs >= 50) {
                    $halfCenturyPoints = CricketPointsService::calculateCricketBonusPoints($format, 'half_century', $player->runs);
                    $totalPoints += $halfCenturyPoints;
                    $pointsBreakdown['half_century'] = $halfCenturyPoints;
                }

                // Century bonus
                if ($player->runs >= 100) {
                    $centuryPoints = CricketPointsService::calculateCricketBonusPoints($format, 'century', $player->runs);
                    $totalPoints += $centuryPoints;
                    $pointsBreakdown['century'] = $centuryPoints;
                }

                // 30+ runs bonus
                if ($player->runs >= 30) {
                    $runBonusPoints = CricketPointsService::calculateCricketBonusPoints($format, 'run_bonus_30', $player->runs);
                    $totalPoints += $runBonusPoints;
                    $pointsBreakdown['run_bonus_30'] = $runBonusPoints;
                }

                // Duck penalty
                if ($player->duck) {
                    $duckPenalty = -2; // Standard duck penalty for T20
                    if ($format == 'ODI') $duckPenalty = -3;
                    if ($format == 'Test') $duckPenalty = -4;
                    $totalPoints += $duckPenalty;
                    $pointsBreakdown['duck'] = $duckPenalty;
                }

                // Bowling points
                if ($player->wicket > 0) {
                    $wicketPoints = CricketPointsService::calculateCricketBonusPoints($format, 'wickets', $player->wicket);
                    $totalPoints += $wicketPoints;
                    $pointsBreakdown['wickets'] = $wicketPoints;
                }

                // Maiden over points
                if ($player->maiden_over > 0) {
                    $maidenPoints = CricketPointsService::calculateCricketBonusPoints($format, 'maiden_over', $player->maiden_over);
                    $totalPoints += $maidenPoints;
                    $pointsBreakdown['maiden_over'] = $maidenPoints;
                }

                // LBW/Bowled bonus
                if ($player->lbw_bowled > 0) {
                    $lbwBowledPoints = CricketPointsService::calculateCricketBonusPoints($format, 'lbw_bowled', $player->lbw_bowled);
                    $totalPoints += $lbwBowledPoints;
                    $pointsBreakdown['lbw_bowled'] = $lbwBowledPoints;
                }

                // Fielding points
                if ($player->catch > 0) {
                    $catchPoints = CricketPointsService::calculateCricketBonusPoints($format, 'catch', $player->catch);
                    $totalPoints += $catchPoints;
                    $pointsBreakdown['catches'] = $catchPoints;
                }

                if ($player->stumbed > 0) {
                    $stumpingPoints = CricketPointsService::calculateCricketBonusPoints($format, 'stumping', $player->stumbed);
                    $totalPoints += $stumpingPoints;
                    $pointsBreakdown['stumping'] = $stumpingPoints;
                }

                if ($player->runouts > 0) {
                    $runOutPoints = CricketPointsService::calculateCricketBonusPoints($format, 'run_out', $player->runouts);
                    $totalPoints += $runOutPoints;
                    $pointsBreakdown['run_outs'] = $runOutPoints;
                }

                // Update player's total points in database
                DB::table('result_matches')
                    ->where('id', $player->id)
                    ->update(['total_points' => $totalPoints]);

                $playerPoints[$player->player_key] = [
                    'player_id' => $player->player_id,
                    'player_key' => $player->player_key,
                    'total_points' => $totalPoints,
                    'breakdown' => $pointsBreakdown
                ];
            }

            return $playerPoints;

        } catch (\Exception $e) {
            Log::error('Error calculating player points: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Send match alert notification
     *
     * @param string $matchKey
     * @param int $sportType
     * @return void
     */
    public static function sendMatchAlertNotification($matchKey, $sportType)
    {
        // Implementation for sending match alert notifications
        // This can be expanded based on your notification system
        Log::info("Match alert notification sent for match: {$matchKey}, sport type: {$sportType}");
    }

    /**
     * Update batting order of players
     *
     * @param string $matchKey
     * @return bool
     */
    public static function updateBattingOrderOfPlayers($matchKey)
    {
        try {
            // Implementation for updating batting order
            // This can be expanded based on your requirements
            Log::info("Batting order updated for match: {$matchKey}");
            return true;
        } catch (\Exception $e) {
            Log::error('Error updating batting order: ' . $e->getMessage());
            return false;
        }
    }
}
