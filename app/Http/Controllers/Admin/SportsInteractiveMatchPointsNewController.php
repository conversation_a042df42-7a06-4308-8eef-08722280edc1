<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use App\Services\PointsConfig;
use App\Services\ScorecardFetcher;
use App\Services\MatchInfoProcessor;
use App\Services\PlayerPointsProcessor;

class SportsInteractiveMatchPointsNewController extends Controller
{
    public function calculate(Request $request)
    {
        if (!$this->validateToken($request->token)) {
            return $this->errorResponse('Invalid Token authentication', 401);
        }

        $gameId = $request->query('game_id');
        if (!$gameId) {
            return $this->errorResponse('game_id required', 422);
        }

        $payload = $this->getCachedPayload($gameId, $request->query('lang', 'en'));
        if (!$payload || empty($payload['Matchdetail'])) {
            return $this->errorResponse('No game found', 404);
        }

        $format = strtoupper(Arr::get($payload, 'Matchdetail.Match.Type', 'T20'));
        if (!isset(PointsConfig::NORMAL[$format])) {
            return $this->errorResponse('Unsupported format', 422);
        }

        $matchDetails = (new MatchInfoProcessor())->process($payload);
        $playerStats = (new PlayerPointsProcessor($format))->process($payload);

        if ($request->response === 'json') {
            return response()->json([
                'status' => 'success',
                'calculated' => now(),
                'game_id' => $gameId,
                'format' => $format,
                'match_details' => $matchDetails,
                'match_player_points' => $playerStats,
            ])->header('Cache-Control', 'public, max-age=60')
              ->header('Vary', 'Accept-Encoding');
        }

        return view('admin.match-points', [
            'gameId' => $gameId,
            'format' => $format,
            'matchInfo' => $matchDetails,
            'matchPlayerPoints' => $playerStats,
        ]);
    }

    private function validateToken($token): bool
    {
        return !empty($token) && $token === config('services.sportz_api.token');
    }

    private function errorResponse(string $message, int $code)
    {
        return response()->json(['status' => 'error', 'message' => $message, 'code' => $code], $code);
    }

    private function getCachedPayload(string $gameId, string $lang)
    {
        $cacheKey = "sports_scorecard_{$gameId}_{$lang}";

        return cache()->remember($cacheKey, 60, function () use ($gameId, $lang) {
            return (new ScorecardFetcher())->fetch($gameId, $lang, 'json');
        });
    }
}
