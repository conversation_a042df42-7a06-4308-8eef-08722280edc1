<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Services\Sportz\SportzInteractiveAPICallingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Log;
use Exception;

class SportzMatchController extends BaseController
{
    public $model = 'Matches';

    // Format mapping constants
    private const FORMAT_MAPPING = [
        'one-day' => [1, 4, 5, 7, 9],
        't10' => [17],
        't20' => [3, 6, 8, 10],
        'test' => [2],
    ];

    public function __construct()
    {
        date_default_timezone_set('Asia/Kolkata');
        View::share('modelName', $this->model);
    }

    /**
     * Import matches from Sportz API
     *
     * @param string $sport_type
     * @return \Illuminate\Http\RedirectResponse
     */
    public function get_matches($sport_type = CRICKET)
    {
        try {
            $sportType = $this->getSportType($sport_type);
            if (!$sportType) {
                Session::flash('flash_error', 'Invalid sport type!');
                return Redirect::back();
            }

            $matchlist = $this->fetchMatchList();
            if (empty($matchlist)) {
                Session::flash('flash_error', 'No matches found from API!');
                return Redirect::back();
            }

            $processedCount = $this->processMatches($matchlist, $sportType);

            Session::flash('flash_notice', "Successfully imported {$processedCount} matches from API!");
            return Redirect::back();

        } catch (Exception $e) {
            Log::error('Error importing matches: ' . $e->getMessage());
            Session::flash('flash_error', 'Error importing matches. Please try again.');
            return Redirect::back();
        }
    }

    /**
     * Get sport type from database
     *
     * @param string $sport_key
     * @return object|null
     */
    private function getSportType($sport_key)
    {
        return DB::table('sport_types')
            ->where('sport_key', $sport_key)
            ->first();
    }

    /**
     * Fetch match list from API
     *
     * @return array
     */
    private function fetchMatchList()
    {
        $from_date = date('Ymd');
        $to_date = date('Ymd', strtotime('+3 days'));
        
        return SportzInteractiveAPICallingService::fetchCricketMatchSchedule($from_date, $to_date);
    }

    /**
     * Process matches from API response
     *
     * @param array $matchlist
     * @param object $sportType
     * @return int
     */
    private function processMatches($matchlist, $sportType)
    {
        $processedCount = 0;
        
        // Pre-load existing teams and matches to reduce database queries
        $existingTeams = $this->getExistingTeams($matchlist);
        $existingMatches = $this->getExistingMatches($matchlist);

        foreach ($matchlist as $match) {
            try {
                $this->processMatch($match, $sportType, $existingTeams, $existingMatches);
                $processedCount++;
            } catch (Exception $e) {
                Log::warning("Failed to process match {$match->match_id}: " . $e->getMessage());
                continue;
            }
        }

        return $processedCount;
    }

    /**
     * Get existing teams from database
     *
     * @param array $matchlist
     * @return \Illuminate\Support\Collection
     */
    private function getExistingTeams($matchlist)
    {
        $teamKeys = [];
        foreach ($matchlist as $match) {
            $teamKeys[] = $match->teama->team_id;
            $teamKeys[] = $match->teamb->team_id;
        }

        return DB::table('teams')
            ->whereIn('team_key', array_unique($teamKeys))
            ->where('sport_type', 1)
            ->get()
            ->keyBy('team_key');
    }

    /**
     * Get existing matches from database
     *
     * @param array $matchlist
     * @return \Illuminate\Support\Collection
     */
    private function getExistingMatches($matchlist)
    {
        $matchKeys = array_column($matchlist, 'match_id');

        return DB::table('listmatches')
            ->whereIn('matchkey', $matchKeys)
            ->where('sport_type', 1)
            ->get()
            ->keyBy('matchkey');
    }

    /**
     * Process individual match
     *
     * @param object $match
     * @param object $sportType
     * @param \Illuminate\Support\Collection $existingTeams
     * @param \Illuminate\Support\Collection $existingMatches
     */
    private function processMatch($match, $sportType, $existingTeams, $existingMatches)
    {
        $matchKey = $match->match_id;
        
        // Get or create teams
        $team1Data = $this->getOrCreateTeam($match->teama, $sportType, $existingTeams);
        $team2Data = $this->getOrCreateTeam($match->teamb, $sportType, $existingTeams);

        // Prepare match data
        $matchData = $this->prepareMatchData($match, $team1Data, $team2Data, $sportType);

        // Insert or update match
        $existingMatch = $existingMatches->get($matchKey);
        
        if (!$existingMatch) {
            DB::table('listmatches')->insert($matchData);
        } elseif ($existingMatch->launch_status === 'pending') {
            $this->updateExistingMatch($matchKey, $matchData, $existingMatch);
        }

        // Handle squad import if available
        if ($match->pre_squad === 'true') {
            // TODO: Implement squad import
            // $this->import_match_squad($matchKey, $match->competition->cid);
        }
    }

    /**
     * Get or create team
     *
     * @param object $teamData
     * @param object $sportType
     * @param \Illuminate\Support\Collection $existingTeams
     * @return array
     */
    private function getOrCreateTeam($teamData, $sportType, $existingTeams)
    {
        $teamKey = $teamData->team_id;
        $existingTeam = $existingTeams->get($teamKey);

        if ($existingTeam) {
            return [
                'id' => $existingTeam->id,
                'display' => $existingTeam->short_name
            ];
        }

        // Create new team
        $newTeamData = [
            'team' => $teamData->name,
            'logo' => '',
            'team_key' => $teamKey,
            'sport_type' => $sportType->id,
            'short_name' => $teamData->short_name,
            'color' => '',
            'created_at' => now()
        ];

        $teamId = DB::table('teams')->insertGetId($newTeamData);

        // Add to existing teams collection for future use
        $existingTeams->put($teamKey, (object)[
            'id' => $teamId,
            'short_name' => $teamData->short_name
        ]);

        return [
            'id' => $teamId,
            'display' => $teamData->short_name
        ];
    }

    /**
     * Prepare match data array
     *
     * @param object $match
     * @param array $team1Data
     * @param array $team2Data
     * @param object $sportType
     * @return array
     */
    private function prepareMatchData($match, $team1Data, $team2Data, $sportType)
    {
        $format = $this->mapFormat($match->format);
        $startDate = Carbon::parse($match->date_start)->addMinutes(330)->format('Y-m-d H:i:s');
        $shortName = $team1Data['display'] . ' VS ' . $team2Data['display'];

        return [
            'name' => $match->title,
            'sport_type' => $sportType->id,
            'short_name' => $shortName,
            'season' => $match->competition->title,
            'title' => $match->title,
            'format' => $format,
            'team1' => $team1Data['id'],
            'team2' => $team2Data['id'],
            'team1display' => $team1Data['display'],
            'team2display' => $team2Data['display'],
            'matchkey' => $match->match_id,
            'series' => 0,
            'start_date' => $startDate,
            'status' => 'notstarted',
            'launch_status' => 'pending',
            'final_status' => 'pending',
            'competition_id' => $match->competition->cid,
            'created_at' => now()
        ];
    }

    /**
     * Update existing match
     *
     * @param string $matchKey
     * @param array $matchData
     * @param object $existingMatch
     */
    private function updateExistingMatch($matchKey, $matchData, $existingMatch)
    {
        // Preserve existing values for certain fields
        $matchData['series'] = $existingMatch->series;
        $matchData['status'] = $existingMatch->status;
        $matchData['launch_status'] = $existingMatch->launch_status;
        $matchData['final_status'] = $existingMatch->final_status;

        DB::table('listmatches')
            ->where('matchkey', $matchKey)
            ->where('sport_type', 1)
            ->update($matchData);
    }

    /**
     * Map format ID to format string
     *
     * @param int $formatId
     * @return string
     */
    private function mapFormat($formatId)
    {
        foreach (self::FORMAT_MAPPING as $format => $ids) {
            if (in_array($formatId, $ids)) {
                return $format;
            }
        }
        
        return 'other';
    }
}
