<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Import Vision11 / Sportz “schedule” feed
 * and synchronise it with our `teams` & `listmatches` tables.
 *
 *  • Only cricket is handled here – copy the same pattern for other sports. 
 *  • All heavy work is done in a **single DB transaction** and with
 *    “set-based” queries wherever possible to keep it fast.
 */
class SportzInteractiveImportService
{
    /** Vision11 client-id passed to every API call */
    protected static $clientId = 'c473b3f16d35';

    /** <PERSON><PERSON> saved in `listmatches.source` so we can easily purge later */
    protected static $source   = 'sportz';

    /**
     * Entry-point called from the console / scheduler.
     *
     * @param  int  $year  Year of the schedule we want (default 2025)
     * @return int         Number of new matches inserted
     * @throws \Throwable  Any failure rolls back and bubbles up
     */
    public static function importCricketMatch($year = 2025): int
    {
        /* ───────────────────────────────────────────────────────────
         *  Hit the Sportz API
         * ─────────────────────────────────────────────────────────*/
        $matches = self::fetchSchedule($year);

        if (empty($matches)) {
            Log::info("[Sportz] No matches returned for $year");
            return 0;
        }

        /* ───────────────────────────────────────────────────────────
         * Resolve look-ups used later
         * ─────────────────────────────────────────────────────────*/
        // “CRICKET” sport_type primary-key
        $sportTypeId = (int) DB::table('sport_types')
            ->where('sport_key', CRICKET)
            ->value('id');

        // All team_keys appearing in the payload (deduplicated)
        $teamKeys = [];
        foreach ($matches as $row) {
            $teamKeys[] = $row['teama_id'];
            $teamKeys[] = $row['teamb_id'];
        }
        $teamKeys = array_values(array_unique($teamKeys));

        //Map of existing teams  [team_key => id]
        $existingTeams = DB::table('teams')
            ->where('sport_type', $sportTypeId)
            ->whereIn('team_key', $teamKeys)
            ->pluck('id', 'team_key')
            ->all();

        /* ───────────────────────────────────────────────────────────
         *Insert any missing teams in bulk
         * ─────────────────────────────────────────────────────────*/
        $toInsertTeams = [];

        foreach ($matches as $row) {
            // home / team-A
            $key = $row['teama_id'];
            if (!isset($existingTeams[$key])) {
                $toInsertTeams[$key] = [
                    'team'       => $row['teama'],
                    'logo'       => '',
                    'team_key'   => $key,
                    'sport_type' => $sportTypeId,
                    'short_name' => $row['teama_short'],
                    'color'      => '',
                    'created_at' => Carbon::now(),
                ];
            }

            // away / team-B
            $key = $row['teamb_id'];
            if (!isset($existingTeams[$key])) {
                $toInsertTeams[$key] = [
                    'team'       => $row['teamb'],
                    'logo'       => '',
                    'team_key'   => $key,
                    'sport_type' => $sportTypeId,
                    'short_name' => $row['teamb_short'],
                    'color'      => '',
                    'created_at' => Carbon::now(),
                ];
            }
        }

        // Actually insert (if anything is missing) and refresh the map
        if ($toInsertTeams) {
            DB::table('teams')->insert(array_values($toInsertTeams));

            $newIds = DB::table('teams')
                ->where('sport_type', $sportTypeId)
                ->whereIn('team_key', array_keys($toInsertTeams))
                ->pluck('id', 'team_key')
                ->all();

            $existingTeams += $newIds;   // merge into the master map
        }

        /* ───────────────────────────────────────────────────────────
         * Prepare existing matches map  [matchkey => launch_status]
         * ─────────────────────────────────────────────────────────*/
        $matchKeys = array_column($matches, 'match_id');
        $existingMatches = DB::table('listmatches')
            ->where('sport_type', $sportTypeId)
            ->whereIn('matchkey', $matchKeys)
            ->pluck('launch_status', 'matchkey')
            ->all();

        /* ───────────────────────────────────────────────────────────
         * Insert / update matches in one transaction
         * ─────────────────────────────────────────────────────────*/
        $inserted   = 0;                 // final return value
        $created_at = date('Y-m-d H:i:s');

        DB::beginTransaction();
        try {
            foreach ($matches as $row) {

                $match_id     = $matchkey = $row['match_id'];
                $name         = $row['teama_display_name'] . ' vs ' . $row['teamb_display_name'];
                $short_name   = $row['teama_short_display_name'] . ' VS ' . $row['teamb_short_display_name'];
                $season       = $row['comp_type'];
                $comp_id      = $row['comp_type_id'];
                $format       = isset($row['match_type']) ? strtolower($row['match_type']) : '';
                $team1id      = $existingTeams[$row['teama_id']];
                $team2id      = $existingTeams[$row['teamb_id']];
                $team1display = $row['teama_short_display_name'];
                $team2display = $row['teamb_short_display_name'];
                $series       = 0;
                $start_date   = date('Y-m-d H:i:s', strtotime('+332 minutes', strtotime($row['start_date'])));
                $status       = 'notstarted';
                $launchStatus = 'pending';
                $finalStatus  = 'pending';

                // ── Decide INSERT vs UPDATE
                if (!isset($existingMatches[$match_id])) {
                    /* ---------- INSERT new match ---------------------------------- */
                    DB::table('listmatches')->insert([
                        'name'           => $name,
                        'sport_type'     => $sportTypeId,
                        'short_name'     => $short_name,
                        'season'         => $season,
                        'title'          => $name,
                        'format'         => $format,
                        'team1'          => $team1id,
                        'team2'          => $team2id,
                        'team1display'   => $team1display,
                        'team2display'   => $team2display,
                        'matchkey'       => $matchkey,
                        'series'         => $series,
                        'start_date'     => $start_date,
                        'status'         => $status,
                        'launch_status'  => $launchStatus,
                        'final_status'   => $finalStatus,
                        'competition_id' => $comp_id,
                        'created_at'     => $created_at,
                        'source'         => self::$source,
                    ]);
                    $inserted++;
                } elseif ($existingMatches[$match_id] === 'pending') {
                    /* ---------- UPDATE only if match still pending ----------------- */
                    DB::table('listmatches')
                        ->where('matchkey', $match_id)
                        ->where('sport_type', $sportTypeId)
                        ->update([
                            'name'           => $name,
                            'short_name'     => $short_name,
                            'season'         => $season,
                            'title'          => $name,
                            'format'         => $format,
                            'team1'          => $team1id,
                            'team2'          => $team2id,
                            'team1display'   => $team1display,
                            'team2display'   => $team2display,
                            'start_date'     => $start_date,
                            'competition_id' => $comp_id,
                            'source'         => self::$source,
                        ]);
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('[Sportz] sync failed: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            throw $e;
        }

        return $inserted;
    }

    /**
     * Call Sportz schedule endpoint and return the “matches” array.
     * Keeps the logic isolated for easy mocking / testing.
     *
     * @param  int   $year
     * @return array
     * @throws \Exception if the endpoint fails or returns invalid JSON
     */
    private static function fetchSchedule($year): array
    {
        $url = "https://assets-vision11.sportz.io/cricket/v1/schedule?"
            . "year={$year}&is_deleted=false&timezone=0000"
            . "&is_live=true&is_upcoming=true&is_recent=false"
            . "&date_list=false&pagination=true&page_size=1&page_number=1"
            . "&lang=en&feed_format=json&client_id=" . self::$clientId;

        $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);

        if (!$json) {
            throw new Exception('Unable to fetch data from Sportz API');
        }

        $payload = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON error: ' . json_last_error_msg());
        }

        return $payload['data']['matches'] ?? [];
    }

    // private static function fetchSchedule(int $year): array
    // {
    //     // ---- Calculate the time window we want --------------------
    //     // $from = Carbon::now('UTC')->startOfDay();          // today 00:00 UTC
    //     // $to   = Carbon::now('UTC')->addWeek()->endOfDay(); // +7 days 23:59 UTC

    //     $from = Carbon::now('UTC')->startOfDay();
    //     $to   = Carbon::now('UTC')->addDays(3)->endOfDay();


    //     $page       = 1;
    //     $pageSize   = 50;      // large page-size → fewer round-trips
    //     $allMatches = [];

    //     do {
    //         // ---- Build paginated URL ------------------------------
    //         $url = "https://assets-vision11.sportz.io/cricket/v1/schedule?"
    //             . "year={$year}&is_deleted=false&timezone=0000"
    //             . "&is_live=true&is_upcoming=true&is_recent=false"
    //             . "&date_list=false&pagination=true"
    //             . "&page_size={$pageSize}&page_number={$page}"
    //             . "&lang=en&feed_format=json&client_id=" . self::$clientId;

    //         $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);
    //         if (!$json) {
    //             throw new Exception('Unable to fetch data from Sportz API');
    //         }

    //         $payload = json_decode($json, true);
    //         if (json_last_error() !== JSON_ERROR_NONE) {
    //             throw new Exception('JSON error: ' . json_last_error_msg());
    //         }

    //         $matches   = $payload['data']['matches'] ?? [];
    //         $hasMore   = $payload['meta']['pagination'] ?? false;
    //         $pageTotal = $payload['meta']['page_size'] ?? $pageSize;

    //         // ---- Filter the current page on the fly ---------------
    //         foreach ($matches as $m) {
    //             // "start_date" comes like  1/1/2025T01:30:00+00:00
    //             $kickOff = Carbon::parse($m['start_date']); // Carbon handles the offset

    //             // Keep only matches in the [today .. +7 days] window
    //             if ($kickOff->between($from, $to)) {
    //                 $allMatches[] = $m;
    //             }

    //             // Optional micro-optimisation:
    //             // if we’ve already passed the +7 day window AND the API
    //             // is sorted ascending, we can break early.
    //             if ($kickOff->greaterThan($to)) {
    //                 $hasMore = false; // stop outer while
    //                 break;
    //             }
    //         }

    //         $page++; // next page (if any)
    //     } while ($hasMore && count($matches) === $pageSize);
    //     echo "<pre>";
    //     print_r($allMatches);
    //     die;
    //     return $allMatches;
    // }
}
