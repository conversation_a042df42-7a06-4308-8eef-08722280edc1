<?php

namespace App\Services;

use App\Services\PointsConfig;
use Illuminate\Support\Arr;

class PlayerPointsProcessor
{
    private string $format;
    private array $totals = [];
    private array $breaks = [];
    private array $roles = [];
    private array $names = [];

    public function __construct(string $format)
    {
        $this->format = $format;
    }

    public function process(array $payload): array
    {
        $this->initializePlayerData($payload);
        $this->processInnings($payload);

        return $this->buildPlayerStats($payload);
    }

    /**
     * Initialize player data (roles, names, starting XI)
     */
    private function initializePlayerData(array $payload): void
    {
        foreach (Arr::get($payload, 'Teams', []) as $teamId => $team) {
            foreach ($team['Players'] ?? [] as $pid => $pData) {
                $this->roles[$pid] = $this->mapRole($pData['Skill'] ?? 1);
                $this->names[$pid] = $pData['Name_Full'] ?? '';

                if (!empty($pData['Confirm_XI'])) {
                    $this->addStat($pid, 'starting_xi', 1, PointsConfig::NORMAL[$this->format]['starting_xi']);
                }
            }
        }
    }

    /**
     * Process all innings data
     */
    private function processInnings(array $payload): void
    {
        $allInnings = array_merge(
            Arr::get($payload, 'Innings', []),
            Arr::get($payload, 'Superover.Innings', [])
        );

        foreach ($allInnings as $inn) {
            if (empty($inn['Batsmen']) && empty($inn['Bowlers'])) {
                continue;
            }

            $this->processBatting($inn);
            $this->processBowling($inn);
            $this->processFielding($inn);
            $this->processDismissalBonus($inn);
        }
    }

    /**
     * Process batting statistics
     */
    private function processBatting(array $inn): void
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $pid = $b['Batsman'] ?? null;
            if (!$pid) continue;

            $runs = (int)($b['Runs'] ?? 0);
            $balls = (int)($b['Balls'] ?? 0);
            $fours = (int)($b['Fours'] ?? 0);
            $sixes = (int)($b['Sixes'] ?? 0);
            $strikeRate = $balls > 0 ? ($runs / $balls) * 100 : 0;

            // Basic batting points
            $this->addStat($pid, 'runs', $runs, $runs); // 1 point per run
            $this->addStat($pid, 'balls', $balls, 0); // Track balls faced

            // Boundary and six points
            $boundaryPoints = $fours * (PointsConfig::BONUS[$this->format]['boundary'] ?? 1);
            $sixPoints = $sixes * (PointsConfig::BONUS[$this->format]['six'] ?? 2);

            $this->addStat($pid, 'boundaries', $fours, $boundaryPoints);
            $this->addStat($pid, 'sixes', $sixes, $sixPoints);

            // Milestone bonuses
            if ($runs >= 100 && (PointsConfig::BONUS[$this->format]['century'] ?? 0) > 0) {
                $this->addStat($pid, 'century', 1, PointsConfig::BONUS[$this->format]['century']);
            } elseif ($runs >= 50) {
                $this->addStat($pid, 'half_century', 1, PointsConfig::BONUS[$this->format]['half_century'] ?? 0);
            }

            // 30+ runs bonus
            if ($runs >= 30 && (PointsConfig::BONUS[$this->format]['run_bonus_30'] ?? 0) > 0) {
                $this->addStat($pid, 'run_bonus_30', 1, PointsConfig::BONUS[$this->format]['run_bonus_30']);
            }

            // Strike rate bonus
            if ($balls >= 10) {
                $srBonus = $this->calculateStrikeRateBonus($strikeRate);
                if ($srBonus != 0) {
                    $this->addStat($pid, 'strike_rate', 1, $srBonus);
                }
            }

            // Duck penalty
            $dismissal = strtolower($b['DismissalType'] ?? '');
            if ($runs == 0 && $dismissal != 'not out' && !empty($dismissal)) {
                $role = $this->roles[$pid] ?? 'batter';
                if ($role != 'bowler') {
                    $this->addStat($pid, 'duck', 1, PointsConfig::NORMAL[$this->format]['duck']);
                }
            }

            // Store additional stats for response
            $this->breaks[$pid]['strike_rate_per'] = ['count' => $strikeRate, 'points' => 0];
            $this->breaks[$pid]['batting_position'] = $b['Batting_Order'] ?? null;
        }
    }

    /**
     * Process bowling statistics
     */
    private function processBowling(array $inn): void
    {
        foreach ($inn['Bowlers'] ?? [] as $b) {
            $pid = $b['Bowler'] ?? null;
            if (!$pid) continue;

            $wickets = (int)($b['Wickets'] ?? 0);
            $overs = $this->oversToFloat($b['Overs'] ?? '0');
            $runs = (int)($b['Runs'] ?? 0);
            $maidens = (int)($b['Maidens'] ?? 0);
            $noballs = (int)($b['Noballs'] ?? 0);
            $wides = (int)($b['Wides'] ?? 0);
            $dots = (int)($b['Dots'] ?? 0);
            $economyRate = $overs > 0 ? $runs / $overs : 0;

            // Basic bowling points
            $wicketPoints = $wickets * PointsConfig::NORMAL[$this->format]['wicket'];
            $maidenPoints = $maidens * (PointsConfig::BONUS[$this->format]['maiden_over'] ?? 0);

            $this->addStat($pid, 'wickets', $wickets, $wicketPoints);
            $this->addStat($pid, 'maidens', $maidens, $maidenPoints);

            // Wicket bonuses
            if ($wickets >= 5 && (PointsConfig::BONUS[$this->format]['wickets_5'] ?? 0) > 0) {
                $this->addStat($pid, 'wickets_5', 1, PointsConfig::BONUS[$this->format]['wickets_5']);
            } elseif ($wickets >= 4 && (PointsConfig::BONUS[$this->format]['wickets_4'] ?? 0) > 0) {
                $this->addStat($pid, 'wickets_4', 1, PointsConfig::BONUS[$this->format]['wickets_4']);
            } elseif ($wickets >= 3 && (PointsConfig::BONUS[$this->format]['wickets_3'] ?? 0) > 0) {
                $this->addStat($pid, 'wickets_3', 1, PointsConfig::BONUS[$this->format]['wickets_3']);
            } elseif ($wickets >= 2 && isset(PointsConfig::BONUS[$this->format]['wickets_2'])) {
                $this->addStat($pid, 'wickets_2', 1, PointsConfig::BONUS[$this->format]['wickets_2']);
            }

            // Economy rate bonus
            if ($overs >= 2) {
                $econBonus = $this->calculateEconomyRateBonus($economyRate);
                if ($econBonus != 0) {
                    $this->addStat($pid, 'economy_rate', 1, $econBonus);
                }
            }

            // Store additional stats for response
            $this->breaks[$pid]['balls_bowled'] = ['count' => $overs * 6, 'points' => 0];
            $this->breaks[$pid]['bowler_runs_conceded'] = ['count' => $runs, 'points' => 0];
            $this->breaks[$pid]['economy_rate_per'] = ['count' => $economyRate, 'points' => 0];
            $this->breaks[$pid]['overs'] = ['count' => $overs, 'points' => 0];
            $this->breaks[$pid]['no_ball'] = ['count' => $noballs, 'points' => 0];
            $this->breaks[$pid]['wides'] = ['count' => $wides, 'points' => 0];
            $this->breaks[$pid]['dots'] = ['count' => $dots, 'points' => 0];
        }
    }

    /**
     * Process fielding statistics
     */
    private function processFielding(array $inn): void
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $dismiss = strtolower(Arr::get($b, 'DismissalType', ''));
            $f1 = Arr::get($b, 'Fielder');

            // Catches
            if ($dismiss === 'caught' && $f1) {
                $this->addStat($f1, 'catches', 1, PointsConfig::NORMAL[$this->format]['catch']);
            }

            // Stumpings
            if (in_array($dismiss, ['st', 'stumped']) && $f1) {
                $this->addStat($f1, 'stumping', 1, PointsConfig::NORMAL[$this->format]['stumping']);
            }

            // Run outs
            if (in_array($dismiss, ['run out', 'ro'])) {
                $thrower = Arr::get($b, 'Fielder');
                $assist1 = Arr::get($b, 'Fielder1');
                $assist2 = Arr::get($b, 'Fielder2');

                if ($thrower) {
                    $this->addStat($thrower, 'run_out_thrower', 1, PointsConfig::NORMAL[$this->format]['run_out']);
                }

                foreach (array_unique(array_filter([$assist1, $assist2])) as $fid) {
                    if ($fid !== $thrower) {
                        $this->addStat($fid, 'run_out_catcher', 1, PointsConfig::NORMAL[$this->format]['run_out']);
                    }
                }
            }
        }
    }



    /**
     * Map skill number to role
     */
    private function mapRole(int $skill): string
    {
        switch($skill) {
            case 1: return 'batter';
            case 2: return 'bowler';
            case 3: return 'all-rounder';
            case 4: return 'wicketkeeper';
            default: return 'batter';
        }
    }

    /**
     * Add stat and calculate points
     */
    private function addStat($pid, string $category, int $count, int $points): void
    {
        if (!isset($this->totals[$pid])) {
            $this->totals[$pid] = 0;
        }

        $this->totals[$pid] += $points;

        // Initialize if not exists
        if (!isset($this->breaks[$pid][$category])) {
            $this->breaks[$pid][$category] = ['count' => 0, 'points' => 0];
        }
        
        // Accumulate counts and points
        $this->breaks[$pid][$category]['count'] += $count;
        $this->breaks[$pid][$category]['points'] += $points;
    }

    /**
     * Convert overs string to float
     */
    private function oversToFloat(string $overs): float
    {
        if (empty($overs) || $overs === '0' || $overs === '0.0') {
            return 0.0;
        }

        if (strpos($overs, '.') !== false) {
            [$wholeOvers, $balls] = explode('.', $overs);
            return (float)$wholeOvers + ((float)$balls / 6);
        }

        return (float)$overs;
    }

    /**
     * Calculate strike rate bonus
     */
    private function calculateStrikeRateBonus(float $strikeRate): int
    {
        $rules = PointsConfig::STRIKE_RATE[$this->format]['rules'] ?? [];

        foreach ($rules as $rule) {
            [$min, $max, $points] = $rule;
            if ($max === null) {
                if ($strikeRate >= $min) return $points;
            } else {
                if ($strikeRate >= $min && $strikeRate <= $max) return $points;
            }
        }

        return 0;
    }

    /**
     * Calculate economy rate bonus
     */
    private function calculateEconomyRateBonus(float $economyRate): int
    {
        $rules = PointsConfig::ECONOMY[$this->format]['rules'] ?? [];

        foreach ($rules as $rule) {
            [$min, $max, $points] = $rule;
            if ($max === null) {
                if ($economyRate >= $min) return $points;
            } else {
                if ($economyRate >= $min && $economyRate <= $max) return $points;
            }
        }

        return 0;
    }

    /**
     * Process dismissal bonuses (LBW/Bowled)
     */
    private function processDismissalBonus(array $inn): void
    {
        foreach ($inn['Batsmen'] ?? [] as $b) {
            $type = strtolower(Arr::get($b, 'DismissalType', ''));
            if (in_array($type, ['b', 'bowled', 'lbw', 'lb'])) {
                $bowler = Arr::get($b, 'Bowler');
                if ($bowler && (PointsConfig::BONUS[$this->format]['lbw_bowled'] ?? 0) > 0) {
                    $this->addStat($bowler, 'lbw_bowled', 1, PointsConfig::BONUS[$this->format]['lbw_bowled']);
                }
            }
        }
    }

    /**
     * Create player-inning mapping
     */
    private function createPlayerInningMap(array $innings): array
    {
        $playerInningMap = [];

        foreach ($innings as $index => $inn) {
            $inningNumber = $index + 1;

            foreach ($inn['Batsmen'] ?? [] as $batsman) {
                $plrid = $batsman['Batsman'] ?? null;
                if ($plrid) {
                    $playerInningMap[$plrid] = [
                        'inning_number' => $inningNumber,
                        'team_id' => $inn['Battingteam'] ?? null,
                    ];
                }
            }

            foreach ($inn['Bowlers'] ?? [] as $bowler) {
                $plrid = $bowler['Bowler'] ?? null;
                if ($plrid) {
                    $playerInningMap[$plrid] = [
                        'inning_number' => $inningNumber,
                        'team_id' => $inn['Bowlingteam'] ?? null,
                    ];
                }
            }
        }

        return $playerInningMap;
    }

    /**
     * Build final player statistics array
     */
    private function buildPlayerStats(array $payload): array
    {
        $rows = [];
        $innings = $payload['Innings'] ?? [];
        $teams = $payload['Teams'] ?? [];

        // Create player-inning mapping
        $playerInningMap = $this->createPlayerInningMap($innings);

        foreach ($this->totals as $pid => $totalPoints) {
            $bk = $this->breaks[$pid] ?? [];

            // Get team info
            $playerTeamId = $playerInningMap[$pid]['team_id'] ?? null;
            $teamShort = '';
            if ($playerTeamId && isset($teams[$playerTeamId])) {
                $teamShort = $teams[$playerTeamId]['Name_Short'] ?? '';
            }

            $cnt = function (array $bk, string $k) {
                return $bk[$k]['count'] ?? 0;
            };

            $pts = function (array $bk, string $k): int {
                return $bk[$k]['points'] ?? 0;
            };

            $runOut = $cnt($bk, 'run_out_thrower') + $cnt($bk, 'run_out_catcher');

            $rows[] = [
                'team_id' => $playerTeamId,
                'inning_number' => $playerInningMap[$pid]['inning_number'] ?? 1,
                'team_name' => $teamShort,
                'player_key' => (int)$pid,
                'player_name' => $this->names[$pid] ?? '',
                'player_role' => $this->roles[$pid] ?? 'batter',
                'batting_position' => $bk['batting_position'] ?? null,
                'start_point_count' => $cnt($bk, 'starting_xi'),
                'start_point' => $pts($bk, 'starting_xi'),
                'playing_xi' => !empty($teams[$playerTeamId]['Players'][$pid]['Confirm_XI'] ?? false),
                'run' => $pts($bk, 'runs'),
                'balls' => $cnt($bk, 'balls'),
                'fours' => $pts($bk, 'boundaries'),
                'fours_count' => $cnt($bk, 'boundaries'),
                'sixs' => $pts($bk, 'sixes'),
                'sixs_count' => $cnt($bk, 'sixes'),
                'strike_rate' => $pts($bk, 'strike_rate'),
                'century' => $pts($bk, 'century'),
                'century_count' => $cnt($bk, 'century'),
                'thirty_runs' => $pts($bk, 'run_bonus_30'),
                'thirty_runs_count' => $cnt($bk, 'run_bonus_30'),
                'wickets' => $pts($bk, 'wickets'),
                'wickets_count' => $cnt($bk, 'wickets'),
                'maidens' => $pts($bk, 'maidens'),
                'maidens_count' => $cnt($bk, 'maidens'),
                'balls_bowled' => $cnt($bk, 'balls_bowled'),
                'bowler_runs_conceded' => $cnt($bk, 'bowler_runs_conceded'),
                'economy_rate_per' => number_format($cnt($bk, 'economy_rate_per'), 2, '.', ''),
                'strike_rate_per' => number_format($cnt($bk, 'strike_rate_per'), 2, '.', ''),
                'overs' => $cnt($bk, 'overs'),
                'no_ball' => $cnt($bk, 'no_ball'),
                'wides' => $cnt($bk, 'wides'),
                'dots' => $cnt($bk, 'dots'),
                'economy_rate' => $pts($bk, 'economy_rate'),
                'economy_rate_count' => $cnt($bk, 'economy_rate'),
                'catch' => $pts($bk, 'catches'),
                'catch_count' => $cnt($bk, 'catches'),
                'run_outs' => $runOut * (PointsConfig::NORMAL[$this->format]['run_out'] ?? 0),
                'run_outs_count' => $runOut,
                'stumped' => $pts($bk, 'stumping'),
                'stumped_count' => $cnt($bk, 'stumping'),
                'lbw_bonus' => $pts($bk, 'lbw_bowled'),
                'lbw_bowled_count' => $cnt($bk, 'lbw_bowled'),
                'total_bonus' => $totalPoints,
                'batting_points' => [
                    'runs' => $pts($bk, 'runs'),
                    'fours' => $pts($bk, 'boundaries'),
                    'sixes' => $pts($bk, 'sixes'),
                    'strike_rate' => $pts($bk, 'strike_rate'),
                    'thirty_runs' => $pts($bk, 'run_bonus_30'),
                    'half_century' => $pts($bk, 'half_century'),
                    'century' => $pts($bk, 'century'),
                    'duck' => $pts($bk, 'duck'),
                ],
                'bowling_points' => [
                    'wickets' => $pts($bk, 'wickets'),
                    'maidens' => $pts($bk, 'maidens'),
                    'economy_rate' => $pts($bk, 'economy_rate'),
                    'lbw_bonus' => $pts($bk, 'lbw_bowled'),
                    'wickets_2' => $pts($bk, 'wickets_2'),
                    'wickets_3' => $pts($bk, 'wickets_3'),
                    'wickets_4' => $pts($bk, 'wickets_4'),
                    'wickets_5' => $pts($bk, 'wickets_5'),
                ],
                'fielding_points' => [
                    'catches' => $pts($bk, 'catches'),
                    'stumping' => $pts($bk, 'stumping'),
                    'run_outs' => $runOut * (PointsConfig::NORMAL[$this->format]['run_out'] ?? 0),
                ],
                'bonus_points' => [
                    'starting_xi' => $pts($bk, 'starting_xi'),
                ],
            ];
        }

        return $rows;
    }
}
