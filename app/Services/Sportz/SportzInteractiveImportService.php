<?php

namespace App\Services\Sportz;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Services\CurlService;
use App\Model\ListMatchDetails;

/**
 * Import Vision11 / Sportz “schedule” feed
 * and synchronise it with our `teams` & `listmatches` tables.
 *
 *  • Only cricket is handled here – copy the same pattern for other sports. 
 *  • All heavy work is done in a **single DB transaction** and with
 *    “set-based” queries wherever possible to keep it fast.
 */
class SportzInteractiveImportService
{
    /** Vision11 client-id passed to every API call */
    protected static $clientId = 'c473b3f16d35';

    /** Marker saved in `listmatches.source` so we can easily purge later */
    protected static $source   = 'sportz';

    /**
     * Entry-point called from the console / scheduler.
     *
     * @param  int  $year  Year of the schedule we want (default 2025)
     * @return int         Number of new matches inserted
     * @throws \Throwable  Any failure rolls back and bubbles up
     */
    public static function importCricketMatch($match_key,$sport_type,$year = 2025): int
    {
        /* 
         *  Hit the Sportz API
         */
        // $matches = self::fetchSchedule($year);
        $matches = SportzInteractiveAPICallingService::fetchSchedule($match_key,$sport_type,$year);

        if (empty($matches)) {
            Log::info("[Sportz] No matches returned for $year");
            return 0;
        }

        /* 
         * Resolve look-ups used later
         */
        // “CRICKET” sport_type primary-key
        $sportTypeId = (int) DB::table('sport_types')
            ->where('sport_key', CRICKET)
            ->value('id');

        // All team_keys appearing in the payload (deduplicated)
        $teamKeys = [];
        foreach ($matches as $row) {
            $teamKeys[] = $row['teama_id'];
            $teamKeys[] = $row['teamb_id'];
        }
        $teamKeys = array_values(array_unique($teamKeys));

        //Map of existing teams  [team_key => id]
        $existingTeams = DB::table('teams')
            ->where('sport_type', $sportTypeId)
            ->whereIn('team_key', $teamKeys)
            ->pluck('id', 'team_key')
            ->all();

        /*
         *Insert any missing teams in bulk
         */
        $toInsertTeams = [];

        foreach ($matches as $row) {
            // home / team-A
            $key = $row['teama_id'];
            if (!isset($existingTeams[$key])) {
                $toInsertTeams[$key] = [
                    'team'       => $row['teama'],
                    'logo'       => '',
                    'team_key'   => $key,
                    'sport_type' => $sportTypeId,
                    'short_name' => $row['teama_short'],
                    'color'      => '',
                    'created_at' => Carbon::now(),
                ];
            }

            // away / team-B
            $key = $row['teamb_id'];
            if (!isset($existingTeams[$key])) {
                $toInsertTeams[$key] = [
                    'team'       => $row['teamb'],
                    'logo'       => '',
                    'team_key'   => $key,
                    'sport_type' => $sportTypeId,
                    'short_name' => $row['teamb_short'],
                    'color'      => '',
                    'created_at' => Carbon::now(),
                ];
            }
        }

        // Actually insert (if anything is missing) and refresh the map
        if ($toInsertTeams) {
            DB::table('teams')->insert(array_values($toInsertTeams));

            $newIds = DB::table('teams')
                ->where('sport_type', $sportTypeId)
                ->whereIn('team_key', array_keys($toInsertTeams))
                ->pluck('id', 'team_key')
                ->all();

            $existingTeams += $newIds;   // merge into the master map
        }

        /*
         * Prepare existing matches map  [matchkey => launch_status]
         */
        $matchKeys = array_column($matches, 'match_id');
        $existingMatches = DB::table('listmatches')
            ->where('sport_type', $sportTypeId)
            ->whereIn('matchkey', $matchKeys)
            ->pluck('launch_status', 'matchkey')
            ->all();

        /* 
         * Insert / update matches in one transaction
         */
        $inserted   = 0;                 // final return value
        $created_at = date('Y-m-d H:i:s');

        DB::beginTransaction();
        try {
            foreach ($matches as $row) {

                $match_id     = $matchkey = $row['match_id'];
                $name         = $row['teama_display_name'] . ' vs ' . $row['teamb_display_name'];
                $short_name   = $row['teama_short_display_name'] . ' VS ' . $row['teamb_short_display_name'];
                $season       = $row['comp_type'];
                $comp_id      = $row['comp_type_id'];
                $format       = isset($row['match_type']) ? strtolower($row['match_type']) : '';
                $team1id      = $existingTeams[$row['teama_id']];
                $team2id      = $existingTeams[$row['teamb_id']];
                $team1display = $row['teama_short_display_name'];
                $team2display = $row['teamb_short_display_name'];
                $series       = 0;
                $start_date   = date('Y-m-d H:i:s', strtotime('+330 minutes', strtotime($row['start_date'])));
                $status       = 'notstarted';
                $launchStatus = 'pending';
                $finalStatus  = 'pending';

                // Decide INSERT vs UPDATE
                if (!isset($existingMatches[$match_id])) {
                    /* ---------- INSERT new match ---------------------------------- */
                    DB::table('listmatches')->insert([
                        'name'           => $name,
                        'sport_type'     => $sportTypeId,
                        'short_name'     => $short_name,
                        'season'         => $season,
                        'title'          => $name,
                        'format'         => $format,
                        'team1'          => $team1id,
                        'team2'          => $team2id,
                        'team1display'   => $team1display,
                        'team2display'   => $team2display,
                        'matchkey'       => $matchkey,
                        'series'         => $series,
                        'start_date'     => $start_date,
                        'status'         => $status,
                        'launch_status'  => $launchStatus,
                        'final_status'   => $finalStatus,
                        'competition_id' => $comp_id,
                        'created_at'     => $created_at,
                        'source'         => self::$source,
                    ]);
                    $inserted++;
                } elseif ($existingMatches[$match_id] === 'pending') {
                    /* ---------- UPDATE only if match still pending ----------------- */
                    DB::table('listmatches')
                        ->where('matchkey', $match_id)
                        ->where('sport_type', $sportTypeId)
                        ->update([
                            'name'           => $name,
                            'short_name'     => $short_name,
                            'season'         => $season,
                            'title'          => $name,
                            'format'         => $format,
                            'team1'          => $team1id,
                            'team2'          => $team2id,
                            'team1display'   => $team1display,
                            'team2display'   => $team2display,
                            'start_date'     => $start_date,
                            'competition_id' => $comp_id,
                            'source'         => self::$source,
                        ]);
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('[Sportz] sync failed: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            throw $e;
        }

        return $inserted;
    }



    public static function importSportzMatchSquad($match_id, $sport_type, $team1_key = null,$series_id="12760")
    {
        try {
            $squads = SportzInteractiveAPICallingService::fetchMatchSquad($team1_key,$series_id);
            // echo "<pre>";print_r($squads);die;
            $players = $squads['teams']['team'][0]['players']['player'];
            $team = $squads['teams']['team'][0];
            if (empty($players)) {
                \Log::info("[Sportz] No matches returned for");
                return 0;
            }
            $sport_type = str_replace(' ', '', strtoupper($sport_type));

            $sportType = DB::table('sport_types')->where('sport_key', $sport_type)->first();

            $newPlayers = [];
            $allNewPlayers = [];
            $i = 0;

            // First check the match is launched or not if yes
            // Generate and store alert notification content for new player
            $getMatch = DB::table('listmatches')->where(['matchkey' => $match_id, 'sport_type' => $sportType->id])->first(['squadstatus']);

            if (!empty($players)) {
                $match_data['squadstatus'] = 'yes';

                DB::table('listmatches')->where('matchkey', $match_id)->where('sport_type', $sportType->id)->update($match_data);
            } else {
                $players = array();
            }

            foreach ($players as $key => $value) {
                if (isset($value['id'])) {
                    $teamkey = $team['id'];
                    $players1 = $value['id'];
                    $playerkey = $players1;
                    $lastMatchPlayed = 0;

                    $findTeamExist = DB::table('teams')->where('team_key', $teamkey)->where('sport_type', $sportType->id)->first();
                    if (!empty($findTeamExist)) {
                        $findp1 = DB::table('playerdetails')->where('player_key', $players1)->where('sport_type', $sportType->id)->first();

                        $findplayerexist = DB::table('players')->where('player_key', $players1)->where('team', $findTeamExist->id)->first();

                        $data['player_name'] = $value['name'];
                        $data['player_key'] = $playerkey;
                        $plaerdetailsdata['fullname'] = $value['name'];
                        $plaerdetailsdata['player_key'] = $playerkey;
                        $plaerdetailsdata['country'] = $value['nationality'] ?? '';
                        $plaerdetailsdata['batting_style'] = $value['sport_specific_keys']['batting_style'] ?? '';
                        $plaerdetailsdata['dob'] = $value['date_of_birth'];


                        //for player Bowling style
                        if (!empty($value['sport_specific_keys']['bowling_style'])) {
                            $plaerdetailsdata['bowling_style'] = $value['sport_specific_keys']['bowling_style'];
                        } else {
                            unset($plaerdetailsdata['bowling_style']);
                        }

                        if (empty($findplayerexist)) {
                            $data['team'] = $findTeamExist->id;

                            //player Role 
                            if ($value['role'] == "Batter") {

                                $data['role'] = 'batsman';
                            } elseif ($value['role'] == "All-Rounder") {

                                $data['role'] = 'allrounder';
                            } elseif ($value['role'] == "Bowler") {

                                $data['role'] = 'bowler';
                            } elseif ($value['role'] == "Wicket Keeper") {

                                $data['role'] = 'keeper';
                            } else {

                                $data['role'] = 'allrounder';
                            }

                            $playerid = DB::table('players')->insertGetId($data);

                            $credit = 0; //fantasy_player_rating
                        } else {
                            $playerid = $findplayerexist->id;
                            $credit = 0; //fantasy_player_rating

                            //player Role 
                            if ($value['role'] == "Batter") {

                                $data['role'] = 'batsman';
                            } elseif ($value['role'] == "All-Rounder") {

                                $data['role'] = 'allrounder';
                            } elseif ($value['role'] == "Bowler") {

                                $data['role'] = 'bowler';
                            } elseif ($value['role'] == "Wicket Keeper") {

                                $data['role'] = 'keeper';
                            } else {

                                $data['role'] = 'allrounder';
                            }
                        }


                        /* insert in player details table*/
                        // Get all playerid in array
                        $allNewPlayers[] = $playerid;
                        if (empty($findp1)) {

                            DB::table('playerdetails')->insert($plaerdetailsdata);
                        } else {

                            DB::table('playerdetails')->where('id', $findp1->id)->update($plaerdetailsdata);
                        }


                        // insert players for a match//

                        $findplayer1entry = DB::table('matchplayers')->where('matchkey', $match_id)->where('playerid', $playerid)->first();
                        if (count(explode("_", $match_id)) > 1) {
                            $main_match_player = DB::table('matchplayers')->where('matchkey', explode("_", $match_id)[0])->where('playerid', $playerid)->first();
                            if (empty($main_match_player)) {
                                continue;
                            }
                        }

                        if (empty($findplayer1entry)) {

                            $matchplayerdata['matchkey'] = $match_id;

                            $matchplayerdata['playerid'] = $playerid;
                            $matchplayerdata['player_key'] = $playerkey;

                            $matchplayerdata['role'] = $data['role'];

                            $matchplayerdata['name'] = $data['player_name'];

                            $matchplayerdata['credit'] = $credit;
                            $matchplayerdata['last_match_played'] = $lastMatchPlayed;

                            $deletedPlayers = DB::table('matchplayers_deleted')->where('matchkey', $match_id)->where('player_key', $playerkey)->where('sport_type', 1)->first();
                            if (empty($deletedPlayers)) {

                                try {
                                    DB::table('matchplayers')->insert($matchplayerdata);
                                } catch (\Illuminate\Database\QueryException $ex) {
                                }
                            }

                            // If add players already done then get new players
                            if ($getMatch->squadstatus == 'yes') {
                                $newPlayers[$i]['player_id'] = $playerid;
                                $newPlayers[$i]['player'] = $data['player_name'];
                                $newPlayers[$i]['team_id'] = $findTeamExist->id;
                                $newPlayers[$i]['team'] = $findTeamExist->team;
                            }
                        } else {

                            // $matchplayerdata['matchkey'] = $match_id;

                            // $matchplayerdata['playerid'] = $playerid;


                            // $matchplayerdata['role'] = $data['role'];

                            // $matchplayerdata['name'] = $data['player_name'];

                            // $matchplayerdata['credit'] = $credit;

                            // DB::table('matchplayers')->where('id', $findplayer1entry->id)->update($matchplayerdata);

                        }
                    }
                }
                $i++;
            }

            // If new players exist during add player
            if (!empty($newPlayers)) {

                $content = '';

                // First get old players of the match
                $oldPlayers = DB::table('matchplayers')
                    ->join('players', 'players.id', 'matchplayers.playerid')
                    ->join('teams', 'teams.id', 'players.team')
                    ->where('matchplayers.matchkey', $match_id)
                    ->where('matchplayers.sport_type', $sportType->id)
                    ->whereNotIn('matchplayers.playerid', $allNewPlayers)
                    ->select('matchplayers.playerid', 'matchplayers.name', 'teams.id as team_id', 'teams.team')
                    ->get();

                $oldPlayers = json_decode(json_encode($oldPlayers));

                $uniqueTeams = [];
                // Iterate through the given array
                foreach ($newPlayers as $item) {
                    if (!isset($uniqueTeams[$item['team_id']])) {
                        $uniqueTeams[$item['team_id']] = $item['team'];
                    }
                }

                // There are some old players who are not in current players
                if (count($oldPlayers) > 0) {

                    foreach ($uniqueTeams as $t_key => $t_row) {

                        $teamExist = array_values(array_unique(array_column($newPlayers, 'team_id')));

                        if (in_array($t_key, $teamExist)) {
                            $content .= $t_row . ' : ';

                            foreach ($newPlayers as $m_key => $row) {
                                if ($t_key == $row['team_id']) {
                                    $content .= $row['player'] . ', ';
                                }
                            }
                        }
                    }

                    $content = trim($content, ', ');
                    $count = (count($newPlayers) == 1) ? ' is ' : ' are ';
                    $content .= $count;
                    $content .= ' in, ';

                    foreach ($uniqueTeams as $t_key => $t_row) {

                        $teamExist = array_values(array_unique(array_column($oldPlayers, 'team_id')));

                        if (in_array($t_key, $teamExist)) {
                            $content .= $t_row . ' : ';

                            foreach ($oldPlayers as $m_key => $row) {
                                if ($t_key == $row->team_id) {
                                    $content .= $row->name . ', ';
                                }
                            }
                        }
                    }

                    $content = trim($content, ', ');
                    $count = (count($oldPlayers) == 1) ? ' is ' : ' are ';
                    $content .= $count;
                    $content .= ' out. ';

                    $content .= 'Check and update your team.';
                } else {

                    if (count($newPlayers) == 1) {

                        $count = 'is';
                        $content .= $newPlayers[0]['player'] . ' (' . $newPlayers[0]['team'] . ') is now available for selection. Check and update yours team!';
                    } else {

                        foreach ($uniqueTeams as $t_key => $t_row) {

                            $teamExist = array_values(array_unique(array_column($newPlayers, 'team_id')));

                            if (in_array($t_key, $teamExist)) {
                                $content .= $t_row . ' : ';

                                foreach ($newPlayers as $m_key => $row) {
                                    if ($t_key == $row['team_id']) {
                                        $content .= $row['player'] . ', ';
                                    }
                                }
                            }
                        }

                        $content = trim($content, ', ');
                        $content .= ' are now available for selection.';
                    }
                }

                // Update / Create alert content in DB
                $updateAlert = ListMatchDetails::updateOrCreate(
                    [
                        'matchkey' => $match_id,
                        'sport_type' => $sportType->id,
                    ],
                    [
                        'alert_notify_content' => $content
                    ]
                );
            }
            // return $squads;
        } catch (\Throwable $e) {
            throw $e;
        }
    }

    // private static function fetchSchedule(int $year): array
    // {
    //     // ---- Calculate the time window we want --------------------
    //     // $from = Carbon::now('UTC')->startOfDay();          // today 00:00 UTC
    //     // $to   = Carbon::now('UTC')->addWeek()->endOfDay(); // +7 days 23:59 UTC

    //     $from = Carbon::now('UTC')->startOfDay();
    //     $to   = Carbon::now('UTC')->addDays(3)->endOfDay();


    //     $page       = 1;
    //     $pageSize   = 50;      // large page-size → fewer round-trips
    //     $allMatches = [];

    //     do {
    //         // ---- Build paginated URL ------------------------------
    //         $url = "https://assets-vision11.sportz.io/cricket/v1/schedule?"
    //             . "year={$year}&is_deleted=false&timezone=0000"
    //             . "&is_live=true&is_upcoming=true&is_recent=false"
    //             . "&date_list=false&pagination=true"
    //             . "&page_size={$pageSize}&page_number={$page}"
    //             . "&lang=en&feed_format=json&client_id=" . self::$clientId;

    //         $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);
    //         if (!$json) {
    //             throw new Exception('Unable to fetch data from Sportz API');
    //         }

    //         $payload = json_decode($json, true);
    //         if (json_last_error() !== JSON_ERROR_NONE) {
    //             throw new Exception('JSON error: ' . json_last_error_msg());
    //         }

    //         $matches   = $payload['data']['matches'] ?? [];
    //         $hasMore   = $payload['meta']['pagination'] ?? false;
    //         $pageTotal = $payload['meta']['page_size'] ?? $pageSize;

    //         // ---- Filter the current page on the fly ---------------
    //         foreach ($matches as $m) {
    //             // "start_date" comes like  1/1/2025T01:30:00+00:00
    //             $kickOff = Carbon::parse($m['start_date']); // Carbon handles the offset

    //             // Keep only matches in the [today .. +7 days] window
    //             if ($kickOff->between($from, $to)) {
    //                 $allMatches[] = $m;
    //             }

    //             // Optional micro-optimisation:
    //             // if we’ve already passed the +7 day window AND the API
    //             // is sorted ascending, we can break early.
    //             if ($kickOff->greaterThan($to)) {
    //                 $hasMore = false; // stop outer while
    //                 break;
    //             }
    //         }

    //         $page++; // next page (if any)
    //     } while ($hasMore && count($matches) === $pageSize);
    //     echo "<pre>";
    //     print_r($allMatches);
    //     die;
    //     return $allMatches;
    // }

}
