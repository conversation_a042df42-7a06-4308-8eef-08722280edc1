<?php

namespace App\Services;

use App\Services\CurlService;

class ScorecardFetcher
{
    public function fetch(string $gameId, string $lang, string $format)
    {
        $clientId = env('SPORTZ_CLIENT_ID', 'c473b3f16d35');
        $url = "https://assets-vision11.sportz.io/cricket/v1/game/scorecard?"
            . "game_id={$gameId}&lang={$lang}&feed_format={$format}&client_id={$clientId}";

        $json = CurlService::makeGetRequestWithHeader($url, ['accept: application/json']);
        $data = json_decode($json, true);

        if (json_last_error()) {
            throw new \Exception('JSON error: ' . json_last_error_msg());
        }

        return $data['data'] ?? [];
    }

    /**
     * Get cached scorecard or fetch from API
     */
    public function getCached(string $gameId, string $lang = 'en', int $cacheTtl = 60): ?array
    {
        $cacheKey = "sports_scorecard_{$gameId}_{$lang}";

        return cache()->remember($cacheKey, $cacheTtl, function () use ($gameId, $lang) {
            try {
                return $this->fetch($gameId, $lang, 'json');
            } catch (\Exception $e) {
                \Log::error("Error fetching scorecard for game_id {$gameId}: " . $e->getMessage());
                return null;
            }
        });
    }

    /**
     * Clear cache for specific game
     */
    public function clearCache(string $gameId, string $lang = 'en'): bool
    {
        $cacheKey = "sports_scorecard_{$gameId}_{$lang}";
        return cache()->forget($cacheKey);
    }
}
