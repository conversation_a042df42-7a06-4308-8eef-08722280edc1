<?php

namespace App\Services;

use Illuminate\Support\Arr;

class MatchInfoProcessor
{
    public function process(array $payload): array
    {
        $md = $payload['Matchdetail'] ?? [];
        $teams = $payload['Teams'] ?? [];

        $homeId = Arr::get($md, 'Team_Home');
        $awayId = Arr::get($md, 'Team_Away');

        return [
            'match_status' => Arr::get($md, 'Status'),
            'match_status_id' => Arr::get($md, 'Status_Id'),
            'code' => Arr::get($md, 'Match.Code'),
            'date' => Arr::get($md, 'Match.Date'),
            'time' => Arr::get($md, 'Match.Time'),
            'venue' => Arr::get($md, 'Venue.Name'),
            'series' => [
                'id' => Arr::get($md, 'Series.Id'),
                'name' => Arr::get($md, 'Series.Name'),
            ],
            'team_home' => [
                'id' => (int)$homeId,
                'name' => Arr::get($teams, "$homeId.Name_Full"),
                'short' => Arr::get($teams, "$homeId.Name_Short"),
            ],
            'team_away' => [
                'id' => (int)$awayId,
                'name' => Arr::get($teams, "$awayId.Name_Full"),
                'short' => Arr::get($teams, "$awayId.Name_Short"),
            ],
        ];
    }
}
