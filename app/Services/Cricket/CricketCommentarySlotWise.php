<?php

namespace App\Services\Cricket;

use DB, Auth;

class CricketCommentarySlotWise
{

    public static function importSlotWisePoint($matchKey, $sportType, $inning = 1)
    {
        try {
            // Retrieve slots for the match
            $slots = DB::table('match_slotes')
                ->leftJoin('slotes_master', 'slotes_master.id', '=', 'match_slotes.slotes_master_id')
                ->where('match_slotes.matchkey', $matchKey)
                ->where('match_slotes.sport_type', $sportType)
                ->when(!Auth::check(), function ($query) use ($inning) {
                    $query->where('slotes_master.inning', $inning);
                })
                ->select(
                    'match_slotes.id',
                    'match_slotes.matchkey',
                    'match_slotes.sport_type',
                    'match_slotes.id as slotes_master_id',
                    'slotes_master.format',
                    'slotes_master.inning',
                    'slotes_master.start_over',
                    'slotes_master.end_over'
                )
                ->orderBy('id', 'ASC')
                ->get();

            // total playing plaer list 
            // $matchPlayingPlayersIDs = DB::table('match_playing_players')
            //     ->where(['match_key' => $matchKey, 'playing_status' => 1, 'one_player_enable' => 1])
            //     ->pluck('match_player_id')->toArray();

            $insertData = [];
            foreach ($slots as $slot) {

                $inning = $slot->inning;
                $match_format = $slot->format ? ucfirst($slot->format) : 'T20';
               
                // Get points for batsmen
                $batsmanPoints = self::getPlayerPoints($matchKey, $sportType, $slot, 'batsman_id', 'batsman_points', $inning);

                // Get points for bowlers
                $bowlerPoints = self::getPlayerPoints($matchKey, $sportType, $slot, 'bowler_id', 'bowler_points', $inning);

                // Get points for first fielders
                $fielderPoints = self::getPlayerPoints($matchKey, $sportType, $slot, 'first_fielder_id', 'first_fielder_point', $inning);
                $fielderPoints = self::getPlayerPoints($matchKey, $sportType, $slot, 'second_fielder_id', 'second_fielder_point', $inning);
                $fielderPoints = self::getPlayerPoints($matchKey, $sportType, $slot, 'first_fielder_id', 'third_fielder_point', $inning);

                // Merge all points into one array
                $batsmanPointsArray = $batsmanPoints ? $batsmanPoints->toArray() : [];
                $bowlerPointsArray = $bowlerPoints ? $bowlerPoints->toArray() : [];
                $fielderPointsArray = $fielderPoints ? $fielderPoints->toArray() : [];

                // Combine bowler and fielder points
                $bowlerFielderResults = self::bowlerFielderPointsUpdate($bowlerPointsArray, $fielderPointsArray);

                // $results = array_merge($batsmanPointsArray, $bowlerPointsArray, $fielderPointsArray);
                $results = array_merge($batsmanPointsArray, $bowlerFielderResults);
                
                // Merge all participating players
                $PlaingPlayerID = array_merge(
                    $batsmanPoints->pluck('player_id')->toArray(),
                    $bowlerPoints->pluck('player_id')->toArray(),
                    $fielderPoints->pluck('player_id')->toArray()
                );

                $inningPlayingPlayerIDs = array_unique($PlaingPlayerID);

                // Get match player IDs for participating players
                $playingMatchPlayerIDs = self::getPlayersID($inningPlayingPlayerIDs, $matchKey)->toArray();
                
                // $results = array_merge($batsmanPoints->toArray(), $bowlerPoints->toArray(), $fielderPoints->toArray());
                // $results = array_merge($bowlerPoints->toArray());

                $count = 1;

                // if($matchKey == '89830' && $slot->id == 9036){
                //     echo '<pre>';
                //     print_r($results);
                //     print_r($slot);
                //     // exit;
                // }

                // Prepare data for batch insert, ensuring it's done slot-wise
                foreach ($results as $key => $result) {

                    // Check if the player point for this slot and player already exists

                    #get Player Role

                    $player_key = $result->player_id;
                    $player = self::getPlayersDetails($result->player_id, $matchKey);

                    #if player not exist in matchplayers and players table then dont stored Point
                    if (empty($player->matchplayers_id)) {
                        // \Log::info('This Player key  not exist in table [matchplayers,players]', ['player_key' => $result->player_id, 'matchkey' => $matchKey]);
                        continue;
                    }

                    $player_role = $player->matchplayers_role ?? '';
                    $match_player_id = $player->matchplayers_id ?? '';
                    // $match_player_id = $player_key ?? '';
                    #cath count
                    $caught_count =
                        ($result->first_fielder_caught_out ?? 0) +
                        ($result->second_fielder_caught_out ?? 0) +
                        ($result->third_fielder_caught_out ?? 0);
                        
                    #Six and Four point calculate
                    $six_points = $result->total_six_hit > 0 ? $result->total_six_hit * SIX_POINTS : 0;

                    $four_points = $result->total_four_hit > 0 ? CricketPointsService::calculateCricketBonusPoints($match_format, 'boundary', $result->total_four_hit) : 0;

                    $catch_points = $caught_count > 0 ? $caught_count * FIELDER_CATCH_POINT : 0;
                    // $wicket_points=$result->total_wicket_taken > 0 ?$result->total_wicket_taken * BOWLER_WICKET_POINT:0;
                    $wicket_points = $result->total_wicket_taken > 0 ? $result->total_wicket_taken * BOWLER_WICKET_POINT : 0;

                    #runout count
                    $runouts_count = $result->runouts_count ?? 0;
                    $runout_catcher_count = $result->runout_catcher_count ?? 0;
                    $runout_thrower_count = $result->runout_thrower_count ?? 0;

                    #runout Points
                    $runouts_count_points = $result->runouts_point ?? 0;
                    $runout_catcher_points = $result->runout_catcher_point ?? 0;
                    $runout_thrower_points = $result->runout_thrower_point ?? 0;

                    #Strike Rate calculation start
                    #Match Format :'T20','ODI', 'T10', 'Test',=>current no bonus point for test match 

                    #strike not apply for bowler
                    $strike_rate_data = $result->player_type == 'batsman_id' ? StrikeRatePointsService::calculateStrikeRatePoints($match_format, $result->bat_run, $result->total_ball) : [];

                    $strike_rate_points = $strike_rate_data['strike_rate_points'] ?? 0;
                    $strike_rate_percentage = $strike_rate_data['strike_rate'] ?? 0;

                    #Strike Rate calculation End 

                    #Economy Rate calculation start
                    $total_balls_bowled = $result->total_balls_bowled ?? 0;
                    $total_runs_conceded = $result->total_runs_conceded ?? 0;

                    $economyRate = 0;
                    $economyPoints = 0;
                    // if ($total_balls_bowled > 0 && $result->player_type !='batsman_id') {
                    if ($total_balls_bowled > 0 &&  $result->player_type == 'bowler_id') {
                        #Economy Points Calculate
                        $economy = EconomyRateService::getEconomyRateAndPoints($total_runs_conceded, $total_balls_bowled, $match_format);
                        $economyRate = $economy['economyRate'] ?? 0;
                        $economyPoints = $economy['points'] ?? 0;
                    }

                    #Economy Rate calculation End

                    #LBW/Bowled Points calculation
                    $lbw_bowled_points = $result->total_lbw_bowled_count ?? 0;
                    $lbw_bowled_counts = $result->total_lbw_bowled_points ?? 0;
                    #LBW/Bowled Points calculation End 

                    $negative_points = self::getNegativePoint($strike_rate_points, $economyPoints);

                    // If player is out on duck then apply negative points
                    // $duckPoints = 0;
                    // $duckCount = 0;
                    // if($matchKey == '89830' && $slot->id == 9036){
                    //     if($result->player_type == 'batsman_id' && $result->player_points == 0){
                    //         $duckPoints = self::getDuckPoint($slot, $result->player_id);
                    //     }
                    // }

                    $stumping_points = 0;
                    # bonus =>100/50/30
                    $thirty_run_bonus = 0;
                    $half_century_bonus = 0;
                    $century_bonus = 0;

                    #for Thirty Run Points
                    if ($result->bat_run >= 30 && $result->bat_run < 50) {
                        $thirty_run_bonus = CricketPointsService::calculateCricketBonusPoints($match_format, 'run_bonus_30', $result->bat_run);
                    }
                    #for Fifty Run Points
                    if ($result->bat_run >= 50 && $result->bat_run < 100 && $match_format != 'T10') {
                        $half_century_bonus = CricketPointsService::calculateCricketBonusPoints($match_format, 'half_century', $result->bat_run);
                    }
                    #for T10 match : when player 50 Plus run then this function call and count half century points
                    if ($result->bat_run >= 50 && $match_format == 'T10') {
                        $half_century_bonus = CricketPointsService::calculateCricketBonusPoints($match_format, 'half_century', $result->bat_run);
                    }

                    #for Century Points
                    if ($result->bat_run >= 100 && $match_format != 'T10') {
                        $century_bonus = CricketPointsService::calculateCricketBonusPoints($match_format, 'century', $result->bat_run);
                    }

                    #Maiden over points
                    $maiden_over_points = 0;
                    $maiden_over_count = 0;

                    #if maiden over count greate than o then return bonus point
                    if ($result->maiden_over_count > 0) {
                        $maiden_over_points = CricketPointsService::calculateCricketBonusPoints($match_format, 'maiden_over', $result->maiden_over_count);
                        $maiden_over_count = $result->maiden_over_count;
                    }

                    #total Points
                    $total_points = $result->total_player_points + $strike_rate_points + $economyPoints + $century_bonus + $half_century_bonus + $thirty_run_bonus + $maiden_over_points + STARTING_XI_POINTS;
                    $prepareData = [
                        'match_key' => $matchKey,
                        'player_role' => $player_role,
                        // 'total_runs_conceded' => $total_runs_conceded,
                        // 'total_balls_bowled' => $total_balls_bowled,
                        'maiden_over' => $maiden_over_points,
                        'maiden_count' => $maiden_over_count,
                        'economy_rate' => $economyRate,
                        'economy' => $economyPoints,
                        'bat_run' => $result->bat_run,
                        // 'total_ball' => $result->total_ball,
                        // 'sport_type' => $sportType,
                        'slot' => $slot->slotes_master_id,
                        'team_key' => $result->team_key,
                        'strike_rate' => $strike_rate_points ?? 0,
                        'strike_rate_percentage' => $strike_rate_percentage ?? 0,
                        'match_format' => $slot->format,
                        'start_over' => $slot->start_over,
                        'end_over' => $slot->end_over,
                        'player_id' => $match_player_id,
                        // 'total_points' => $result->total_player_points,
                        'total_points' => $total_points ?? 0,
                        'inning_number' => $result->inning_number,
                        'player_points' => $result->player_points,
                        'wickets' => $wicket_points ?? 0,
                        'wicket_count' => $result->total_wicket_taken ?? 0,
                        'century' => $century_bonus ?? 0,
                        'thirty_runs' => $thirty_run_bonus ?? 0,
                        'fifty_runs' => $half_century_bonus ?? 0,
                        'catch_count' => $caught_count ?? 0,
                        'catch' => $catch_points ?? 0, //points
                        //runout points 
                        'runouts' => $runouts_count_points ?? 0,
                        'runout_catcher' => $runout_catcher_points ?? 0,
                        'runout_thrower' => $runout_thrower_points ?? 0,
                        //runout_count
                        'runouts_count' =>  $runouts_count ?? 0,
                        'runout_catcher_count' =>  $runout_catcher_count ?? 0,
                        'runout_thrower_count' =>  $runout_thrower_count ?? 0,
                        //lbw_bowled
                        'lbw_bowled' =>  $lbw_bowled_points ?? 0,
                        'lbw_bowled_count' =>  $lbw_bowled_counts ?? 0,
                        'sixs' => $six_points ?? 0,
                        'six_count' => $result->total_six_hit ?? 0,
                        'fours' => $four_points ?? 0,
                        'four_count' => $result->total_four_hit ?? 0,
                        'runs' => $result->bat_run ?? 0,
                        'negative' => $negative_points ?? 0,
                        'startingpoints' => STARTING_XI_POINTS,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];

                    $where = [
                        'match_key' => $matchKey,
                        'slot' => $slot->slotes_master_id,
                        'player_id' => $match_player_id,
                        'inning_number' => $result->inning_number
                    ];

                    $exists = DB::table('commentary_slot_wise_points')
                    ->where($where)
                    ->exists();
                    if (!$exists) {
                        DB::table('commentary_slot_wise_points')->insert($prepareData);
                    } else {
                        DB::table('commentary_slot_wise_points')->where($where)->update($prepareData);
                    }   
                }

                // if($matchKey == '89830' && $slot->id == 9036){
                //     exit;
                // }

                #match slots players list
                $matchPlayingPlayersIDs = DB::table('match_slot_players')
                    ->where(['matchkey' => $matchKey, 'slotes_id' => $slot->id, 'deleted_at' => NULL])
                    ->pluck('match_player_id')->toArray();

                // Process non-playing players
                $nonPlayingPlayerIDs = array_filter(
                    array_diff($matchPlayingPlayersIDs, $playingMatchPlayerIDs),
                    function ($playerID) {
                        return !empty($playerID); // Exclude empty or null values
                    }
                );
                
                $nonPlayingPlayerIDs = array_unique($nonPlayingPlayerIDs);
                self::ProcessNonPlayingPlayersPoints($nonPlayingPlayerIDs, $matchKey, $slot->slotes_master_id,$inning, $slot->format, $slot->start_over, $slot->end_over);
            }
            
        } catch (\Exception $e) {
            // \Log::error('CricketCommentarySlotWise->getPlayerPoints:Failed insert data', ['error' => $e->getMessage()]);
        }
    }

    // Merge bowler and fielder points / code refined on 2025-05-02
    public static function bowlerFielderPointsUpdate($bowlerPointsArray, $fielderPointsArray){
        
        $mergedPlayers = [];

        $playerData = array_merge($bowlerPointsArray, $fielderPointsArray);

        foreach ($playerData as $player) {
            $key = $player->player_id;

            if (!isset($mergedPlayers[$key])) {
                $mergedPlayers[$key] = clone $player;
            } else {
                // List of fields to add
                $addFields = [
                    'player_points',
                    'total_lbw_bowled_count',
                    'total_lbw_bowled_points',
                    'total_balls_bowled',
                    'total_runs_conceded',
                    'maiden_over_count',
                    'total_wicket_taken',
                    'first_fielder_caught_out',
                    'second_fielder_caught_out',
                    'runouts_point',
                    'runout_thrower_point',
                    'runout_catcher_point',
                    'runout_thrower_count',
                    'runout_catcher_count',
                    'runouts_count',
                    'total_player_points'
                ];

                // Add numeric fields
                foreach ($addFields as $field) {
                    if (isset($player->$field)) {
                        $mergedPlayers[$key]->$field += $player->$field;
                    }
                }

                // total_ball only from bowler_id
                if ($player->player_type === 'bowler_id') {
                    $mergedPlayers[$key]->total_ball = $player->total_ball;
                }

                // Preserve any other field if not already set
                foreach ($player as $field => $value) {
                    if (!isset($mergedPlayers[$key]->$field)) {
                        $mergedPlayers[$key]->$field = $value;
                    }
                }
            }
        }

        return $mergedPlayers;
    }

    #get Player po
    public static function getPlayerPoints($matchKey, $sportType, $slot, $player_id, $pointsField, $inning)
    {
        try {
            // $slot->start_over = $slot->start_over == 1 ? 0 : $slot->start_over;
            $start_over = ($slot->start_over == 1) ? 0 : ($slot->start_over - 1);
            $end_over = $slot->end_over - 1;
            
            $result = DB::table('cricket_commentary')
                ->where('match_key', $matchKey)
                ->where('inning_number', $inning)
                // ->where('sport_type', $sportType)
                ->whereBetween('match_over', [$start_over, $end_over])
                ->whereNotNull($player_id)  // Ensure player_id is not null
                ->where($player_id, '!=', '') // Ensure player_id is not an empty string
                ->select(
                    'match_key',
                    DB::raw("inning_number as inning_number"),
                    DB::raw("'$player_id' as player_type"), // Add player_type column
                    DB::raw("$player_id as player_id"),
                    DB::raw("SUM($pointsField) as player_points"),
                    DB::raw("
                        CASE
                            WHEN $player_id = batsman_id THEN batting_team_id 
                            ELSE fielding_team_id 
                        END AS team_key
                    "),

                    #for LBW/BOWLED
                    DB::raw('
                        (SUM(CASE WHEN ' . $player_id . ' = bowler_id AND lbw_bowled = 1 THEN 1 ELSE 0 END) + 
                        SUM(CASE WHEN ' . $player_id . ' = bowler_id AND bowler_bowled = 1 THEN 1 ELSE 0 END)
                        ) as total_lbw_bowled_count
                    '),


                    DB::raw('SUM(CASE 
                        WHEN ' . $player_id . ' = bowler_id AND lbw_bowled = 1 THEN IFNULL(lbw_bowled_points, 0)
                        WHEN ' . $player_id . ' = bowler_id AND bowler_bowled = 1 THEN IFNULL(bowler_bowled_points, 0)
                        ELSE 0
                    END) as total_lbw_bowled_points'),


                    #for Economy Rate
                    // DB::raw('COUNT(CASE WHEN ' . $player_id . ' = bowler_id AND match_event = "ball" THEN IFNULL(match_ball, 0) ELSE 0 END) as total_balls_bowled'),
                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = bowler_id AND  wideball=0 AND noball=0 AND match_ball IS NOT NULL THEN 1 ELSE 0 END) as total_balls_bowled'),


                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = bowler_id  THEN IFNULL(match_run, 0) ELSE 0 END) as total_runs_conceded'),

                    # Batting contributions

                    DB::raw('COUNT(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" AND match_ball=6 AND wideball=0 AND noball=0 THEN IFNULL(match_ball, 0) ELSE 0 END) as total_ball'),
                    // DB::raw('SUM(CASE WHEN batsman_id = ' . $player_id . ' AND match_event = "ball" AND match_ball = 6 THEN IFNULL(balls_faced, 0) ELSE 0 END) as total_ball'),


                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" THEN IFNULL(six, 0) ELSE 0 END) as total_six_hit'),


                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" THEN IFNULL(four, 0) ELSE 0 END) as total_four_hit'),

                    // DB::raw('SUM(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" THEN IFNULL(bat_run, 0) ELSE 0 END) as bat_run'),
                    DB::raw('SUM(CASE WHEN batsman_id = ' . $player_id . ' AND match_event = "ball" THEN IFNULL(bat_run, 0) ELSE 0 END) as bat_run'),


                    #maiden_over_count
                    // DB::raw('COUNT(DISTINCT CASE WHEN ' . $player_id . ' = bowler_id AND match_run = 0 THEN match_over ELSE NULL END) as maiden_over_count'),
                    // DB::raw('COUNT(DISTINCT CASE WHEN ' . $player_id . ' = bowler_id AND match_ball >=1 AND match_ball <=6 AND match_run = 0 THEN match_over ELSE NULL END) as maiden_over_count'),
                    DB::raw('
                        COUNT(DISTINCT CASE 
                            WHEN ' . $player_id . ' = bowler_id 
                            AND match_ball >= 1 
                            AND match_ball <= 6 
                            AND (
                                SELECT SUM(match_run) 
                                FROM cricket_commentary sub 
                                WHERE sub.match_over = cricket_commentary.match_over 
                                AND sub.bowler_id = cricket_commentary.bowler_id
                                GROUP BY sub.match_over
                                HAVING SUM(match_run) = 0
                            ) THEN match_over 
                            ELSE NULL 
                        END) as maiden_over_count
                    '),

                    // Points for the bowler
                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = bowler_id THEN IFNULL(bowler_wicket, 0) ELSE 0 END) as total_wicket_taken'),

                    // Points for fielder (caught)
                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = first_fielder_id AND match_event = "wicket" AND dismissal = "caught" THEN IFNULL(first_fielder_caught, 0) ELSE 0 END) as first_fielder_caught_out'),

                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = second_fielder_id AND match_event = "wicket" AND dismissal = "caught" THEN IFNULL(second_fielder_caught, 0) ELSE 0 END) as second_fielder_caught_out'),



                    #runouts_point ,runout_thrower_point,runout_catcher_count
                    DB::raw('SUM(CASE WHEN match_event = "wicket" AND dismissal = "runout"  THEN IFNULL(runouts_point, 0) ELSE 0 END) as runouts_point'),

                    DB::raw('SUM(CASE WHEN match_event = "wicket" AND dismissal = "runout"  THEN IFNULL(runout_thrower_point, 0) ELSE 0 END) as runout_thrower_point'),

                    DB::raw('SUM(CASE WHEN match_event = "wicket" AND dismissal = "runout"  THEN IFNULL(runout_catcher_point, 0) ELSE 0 END) as runout_catcher_point'),


                    DB::raw('SUM(CASE WHEN  match_event = "wicket" AND dismissal = "runout" THEN IFNULL(runout_thrower, 0) ELSE 0 END) as runout_thrower_count'),

                    DB::raw('SUM(CASE WHEN  match_event = "wicket" AND dismissal = "runout" THEN IFNULL(runout_catcher, 0) ELSE 0 END) as runout_catcher_count'),

                    DB::raw('SUM(CASE WHEN  match_event = "wicket" AND dismissal = "runout" THEN IFNULL(runouts, 0) ELSE 0 END) as runouts_count'),

                    // Total combined points
                    DB::raw('SUM(
                    CASE WHEN ' . $player_id . ' = bowler_id THEN IFNULL(bowler_points, 0) ELSE 0 END +
                    CASE WHEN ' . $player_id . ' = batsman_id THEN IFNULL(batsman_points, 0) ELSE 0 END +
                    CASE WHEN ' . $player_id . ' = first_fielder_id AND match_event = "wicket"  THEN IFNULL(first_fielder_point, 0) ELSE 0  END +
                    CASE WHEN ' . $player_id . ' = second_fielder_id AND match_event = "wicket"  THEN IFNULL(second_fielder_point, 0) ELSE 0 END +
                    CASE WHEN ' . $player_id . ' = third_fielder_id AND match_event = "wicket" THEN IFNULL(third_fielder_point, 0) ELSE 0 END
                ) as total_player_points')

                )
                ->groupBy('match_key', 'player_id', 'player_type')
                ->havingRaw('player_id IS NOT NULL')
                ->get();
            return $result;
        } catch (\Exception $e) {
            // \Log::error('CricketCommentarySlotWise->getPlayerPoints: Error', ['error' => $e->getMessage()]);
        }
    }

    // Calculate negative points based on strike rate and economy rate
    public static function getNegativePoint($strike_rate_points = 0, $economyPoints = 0)
    {
        $negative_points = 0;
        if ($strike_rate_points < 0) {
            $negative_points += abs($strike_rate_points);
        }

        if ($economyPoints < 0) {
            $negative_points += abs($economyPoints);
        }
        return $negative_points;
    }

    // Calculate duck points If player out on 0 runs
    // public static function getDuckPoint($slot, $playerId = 0)
    // {     
    //     echo '<pre>';
    //     echo 'HI';
    //     print_r($slot);
    //     print_r($playerId);
    //     // exit;
        

    //     $playerRuns = DB::table('cricket_commentary')
    //         ->where('match_key', $slot->matchkey)
    //         ->where('inning_number', $slot->inning)
    //         ->where('batsman_id', $playerId)
    //         ->where('batsman_id', $playerId)
    //         ->sum('match_run');

    //     print_r($playerRuns);
    //     exit;
    // }

    #get Player details :
    public static function getPlayersDetails($player_key, $matchKey)
    {
        $player = DB::table('matchplayers as mp')
            ->join('players as p', 'mp.playerid', '=', 'p.id')
            // ->join('teams as t', 'p.team', '=', 't.id') // Add join for team
            ->where('mp.sport_type', 1)
            ->where('mp.matchkey', $matchKey)
            ->where('mp.player_key', $player_key)
            ->select('mp.role as matchplayers_role', 'mp.id as matchplayers_id')
            // ->select('mp.role as matchplayers_role', 'mp.id as matchplayers_id','t.team_key')
            ->first();
        return $player;
    }

    #get players details by matchplayer primary id 
    public static function getPlayersDetailsBYMatchPlayerId($matchplayers_id, $matchKey)
    {
        $player = DB::table('matchplayers as mp')
            ->join('players as p', 'mp.playerid', '=', 'p.id')
            ->join('teams as t', 'p.team', '=', 't.id') // Add join for team
            ->where('mp.sport_type', 1)
            ->where('mp.matchkey', $matchKey)
            ->where('mp.id', $matchplayers_id)
            // ->select('mp.role as matchplayers_role', 'mp.id as matchplayers_id')
            ->select('mp.role as matchplayers_role', 'mp.id as matchplayers_id','t.team_key')
            ->first();
        return $player;
    }

    public static function getPlayersID(array $player_keys, $matchKey)
    {
        return DB::table('matchplayers as mp')
            ->join('players as p', 'mp.playerid', '=', 'p.id')
            ->where('mp.sport_type', 1)
            ->where('mp.matchkey', $matchKey)
            ->whereIn('mp.player_key', $player_keys)
            ->pluck('mp.id');
    }

    #process for Non playing player points
    public static function ProcessNonPlayingPlayersPoints($nonPlayingPlayerIDs, $matchKey, $slotes_master_id, $inning, $format, $start_over, $end_over)
    {
        foreach ($nonPlayingPlayerIDs as $nonPlayingPlayerID) {
            $player = self::getPlayersDetailsBYMatchPlayerId($nonPlayingPlayerID, $matchKey);
            if (empty($player->matchplayers_id)) {
                continue;
            }
            $player_role = $player->matchplayers_role ?? '';
            $match_player_id = $player->matchplayers_id ?? '';
            $team_key = $player->team_key ?? 9999999;

            $defaultPointsData = [
                'match_key' => $matchKey,
                'player_role' => $player_role,
                'maiden_over' => 0,
                'maiden_count' => 0,
                'economy_rate' => 0,
                'economy' => 0,
                'bat_run' => 0,
                'slot' => $slotes_master_id,
                'team_key' => $team_key,
                'strike_rate' => 0,
                'strike_rate_percentage' => 0,
                'match_format' => $format,
                'start_over' => $start_over,
                'end_over' => $end_over,
                'player_id' => $match_player_id,
                'total_points' => 4,
                'inning_number' => $inning,
                'player_points' => 0,
                'wickets' => 0,
                'wicket_count' =>  0,
                'century' => 0,
                'thirty_runs' => 0,
                'fifty_runs' => 0,
                'catch_count' => 0,
                'catch' => 0,
                'runouts' => 0,
                'runout_catcher' => 0,
                'runout_thrower' => 0,
                'runouts_count' =>  0,
                'runout_catcher_count' => 0,
                'runout_thrower_count' => 0,
                'lbw_bowled' =>  0,
                'lbw_bowled_count' =>  0,
                'sixs' => 0,
                'six_count' => 0,
                'fours' => 0,
                'four_count' => 0,
                'runs' => 0,
                'negative' =>  0,
                'startingpoints' => STARTING_XI_POINTS,
                'created_at' => now(),
                'updated_at' => now()
            ];
            $exists = DB::table('commentary_slot_wise_points')
                ->where([
                    'match_key' => $matchKey,
                    'slot' => $slotes_master_id,
                    'player_id' => $match_player_id,
                    'inning_number' => $inning
                ])->exists();

            if (!$exists) {
                DB::table('commentary_slot_wise_points')->insert($defaultPointsData);
            }
        }
    }

    #get Player po
    public static function getPlayerPointsV2($matchKey, $sportType, $slot, $player_id, $pointsField, $inning)
    {
        try {
            $start_over = ($slot->start_over == 1) ? 0 : ($slot->start_over - 1);
            $end_over = $slot->end_over - 1;
            
            $result = DB::table('cricket_commentary')
                ->where('match_key', $matchKey)
                ->where('inning_number', $inning)
                ->whereBetween('match_over', [$start_over, $end_over])
                ->whereNotNull($player_id)  // Ensure player_id is not null
                ->where($player_id, '!=', '') // Ensure player_id is not an empty string
                ->select(
                    'match_key',
                    DB::raw("inning_number as inning_number"),
                    DB::raw("'$player_id' as player_type"), // Add player_type column
                    DB::raw("$player_id as player_id"),
                    DB::raw("SUM($pointsField) as player_points"),
                    DB::raw("
                        CASE
                            WHEN $player_id = batsman_id THEN batting_team_id 
                            ELSE fielding_team_id 
                        END AS team_key
                    "),

                    #for LBW/BOWLED
                    DB::raw('
                        (SUM(CASE WHEN ' . $player_id . ' = bowler_id AND lbw_bowled = 1 THEN 1 ELSE 0 END) + 
                        SUM(CASE WHEN ' . $player_id . ' = bowler_id AND bowler_bowled = 1 THEN 1 ELSE 0 END)
                        ) as total_lbw_bowled_count
                    '),

                    DB::raw('SUM(CASE 
                        WHEN ' . $player_id . ' = bowler_id AND lbw_bowled = 1 THEN IFNULL(lbw_bowled_points, 0)
                        WHEN ' . $player_id . ' = bowler_id AND bowler_bowled = 1 THEN IFNULL(bowler_bowled_points, 0)
                        ELSE 0
                    END) as total_lbw_bowled_points'),

                    #for Economy Rate
                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = bowler_id AND  wideball=0 AND noball=0 AND match_ball IS NOT NULL THEN 1 ELSE 0 END) as total_balls_bowled'),

                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = bowler_id  THEN IFNULL(match_run, 0) ELSE 0 END) as total_runs_conceded'),

                    # Batting contributions
                    DB::raw('COUNT(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" AND match_ball=6 AND wideball=0 AND noball=0 THEN IFNULL(match_ball, 0) ELSE 0 END) as total_ball'),

                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" THEN IFNULL(six, 0) ELSE 0 END) as total_six_hit'),

                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = batsman_id AND match_event = "ball" THEN IFNULL(four, 0) ELSE 0 END) as total_four_hit'),

                    DB::raw('SUM(CASE WHEN batsman_id = ' . $player_id . ' AND match_event = "ball" THEN IFNULL(bat_run, 0) ELSE 0 END) as bat_run'),

                    #maiden_over_count
                    DB::raw('
                        COUNT(DISTINCT CASE 
                            WHEN ' . $player_id . ' = bowler_id 
                            AND match_ball >= 1 
                            AND match_ball <= 6 
                            AND (
                                SELECT SUM(match_run) 
                                FROM cricket_commentary sub 
                                WHERE sub.match_over = cricket_commentary.match_over 
                                AND sub.bowler_id = cricket_commentary.bowler_id
                                GROUP BY sub.match_over
                                HAVING SUM(match_run) = 0
                            ) THEN match_over 
                            ELSE NULL 
                        END) as maiden_over_count
                    '),

                    // Points for the bowler
                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = bowler_id THEN IFNULL(bowler_wicket, 0) ELSE 0 END) as total_wicket_taken'),

                    // Points for fielder (caught)
                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = first_fielder_id AND match_event = "wicket" AND dismissal = "caught" THEN IFNULL(first_fielder_caught, 0) ELSE 0 END) as first_fielder_caught_out'),

                    DB::raw('SUM(CASE WHEN ' . $player_id . ' = first_fielder_id AND match_event = "wicket" AND dismissal = "caught" THEN IFNULL(second_fielder_caught, 0) ELSE 0 END) as second_fielder_caught_out'),

                    #runouts_point ,runout_thrower_point,runout_catcher_count
                    DB::raw('SUM(CASE WHEN match_event = "wicket" AND dismissal = "runout"  THEN IFNULL(runouts_point, 0) ELSE 0 END) as runouts_point'),

                    DB::raw('SUM(CASE WHEN match_event = "wicket" AND dismissal = "runout"  THEN IFNULL(runout_thrower_point, 0) ELSE 0 END) as runout_thrower_point'),

                    DB::raw('SUM(CASE WHEN match_event = "wicket" AND dismissal = "runout"  THEN IFNULL(runout_catcher_point, 0) ELSE 0 END) as runout_catcher_point'),


                    DB::raw('SUM(CASE WHEN  match_event = "wicket" AND dismissal = "runout" THEN IFNULL(runout_thrower, 0) ELSE 0 END) as runout_thrower_count'),

                    DB::raw('SUM(CASE WHEN  match_event = "wicket" AND dismissal = "runout" THEN IFNULL(runout_catcher, 0) ELSE 0 END) as runout_catcher_count'),

                    DB::raw('SUM(CASE WHEN  match_event = "wicket" AND dismissal = "runout" THEN IFNULL(runouts, 0) ELSE 0 END) as runouts_count'),

                    // Total combined points
                    DB::raw('SUM(
                    CASE WHEN ' . $player_id . ' = bowler_id THEN IFNULL(bowler_points, 0) ELSE 0 END +
                    CASE WHEN ' . $player_id . ' = batsman_id THEN IFNULL(batsman_points, 0) ELSE 0 END +
                    CASE WHEN ' . $player_id . ' = first_fielder_id AND match_event = "wicket"  THEN IFNULL(first_fielder_point, 0) ELSE 0  END +
                    CASE WHEN ' . $player_id . ' = second_fielder_id AND match_event = "wicket"  THEN IFNULL(second_fielder_point, 0) ELSE 0 END +
                    CASE WHEN ' . $player_id . ' = third_fielder_id AND match_event = "wicket" THEN IFNULL(third_fielder_point, 0) ELSE 0 END
                ) as total_player_points')

                )
                ->groupBy('match_key', 'player_id', 'player_type')
                ->havingRaw('player_id IS NOT NULL')
                ->get();
            return $result;
        } catch (\Exception $e) {
            // \Log::error('CricketCommentarySlotWise->getPlayerPoints: Error', ['error' => $e->getMessage()]);
        }
    }
}
