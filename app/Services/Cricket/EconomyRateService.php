<?php

namespace App\Services\Cricket;

class EconomyRateService
{
    /**
     * Calculate the economy rate and get points based on runs, balls, format, and overs bowled.
     *
     * @param int $runs
     * @param int $balls
     * @param string $format
     * @param float $oversBowled
     * @return array
     */
    public static function getEconomyRateAndPoints(int $runs, int $balls, string $format, float $oversBowled=null): array
    {

        if ($oversBowled === null) {
            $oversBowled = $balls / 6;
        }

        if ($balls === 0) {
            return ['economyRate' => 0.0, 'points' => 0]; // Avoid division by zero
        }

        // Calculate economy rate
        $overs = $balls / 6;
        $economyRate = $runs / $overs;

        // Define points ranges for each format
        $pointsMap = [
            'T20' => [
                ['minOvers' => 2, 'min' => 0, 'max' => 5, 'points' => 6],
                ['minOvers' => 2, 'min' => 5, 'max' => 5.9, 'points' => 4],
                ['minOvers' => 2, 'min' => 5.9, 'max' => 7, 'points' => 2],
                ['minOvers' => 2, 'min' => 10, 'max' => 11, 'points' => -2],
                ['minOvers' => 2, 'min' => 11, 'max' => 12, 'points' => -4],
                ['minOvers' => 2, 'min' => 12, 'max' => INF, 'points' => -6],
            ],
            'ODI' => [
                ['minOvers' => 5, 'min' => 0, 'max' => 2.5, 'points' => 6],
                ['minOvers' => 5, 'min' => 2.5, 'max' => 3.49, 'points' => 4],
                ['minOvers' => 5, 'min' => 3.5, 'max' => 4.5, 'points' => 2],
                ['minOvers' => 5, 'min' => 8.1, 'max' => 9, 'points' => -4],
                ['minOvers' => 5, 'min' => 9, 'max' => INF, 'points' => -6],
            ],
            'One-day' => [
                ['minOvers' => 5, 'min' => 0, 'max' => 2.5, 'points' => 6],
                ['minOvers' => 5, 'min' => 2.5, 'max' => 3.49, 'points' => 4],
                ['minOvers' => 5, 'min' => 3.5, 'max' => 4.5, 'points' => 2],
                ['minOvers' => 5, 'min' => 8.1, 'max' => 9, 'points' => -4],
                ['minOvers' => 5, 'min' => 9, 'max' => INF, 'points' => -6],
            ],
            'T10' => [
                ['minOvers' => 1, 'min' => 0, 'max' => 7, 'points' => 6],
                ['minOvers' => 1, 'min' => 7, 'max' => 7.99, 'points' => 4],
                ['minOvers' => 1, 'min' => 8, 'max' => 9, 'points' => 2],
                ['minOvers' => 1, 'min' => 14, 'max' => 15, 'points' => -2],
                ['minOvers' => 1, 'min' => 15.1, 'max' => 16, 'points' => -4],
                ['minOvers' => 1, 'min' => 16, 'max' => INF, 'points' => -6],
            ]
        ];

        // Return 0 points for formats not listed (e.g., Test)
        if (!isset($pointsMap[$format])) {
            return ['economyRate' => $economyRate, 'points' => 0];
        }

        // Check if the overs bowled meets the minimum required for points calculation
        if ($oversBowled < $pointsMap[$format][0]['minOvers']) {
            return ['economyRate' => $economyRate, 'points' => 0];
        }

        // Find the appropriate points for the given economy rate
        foreach ($pointsMap[$format] as $range) {
            if ($economyRate >= $range['min'] && $economyRate <= $range['max']) {
                return ['economyRate' => $economyRate, 'points' => $range['points']];
            }
        }

        return ['economyRate' => $economyRate, 'points' => 0]; // Default points if no range matched
    }
}
