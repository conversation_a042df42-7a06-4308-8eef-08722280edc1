<?php

namespace App\Services\Cricket;

use App\global_constants;
use App\Services\CurlService;
use mjanssen\BreadcrumbsBundle\Breadcrumbs as Breadcrumb;
use <PERSON><PERSON>, <PERSON>, <PERSON>fig, <PERSON><PERSON>, <PERSON><PERSON>, DB, File, Hash, Input, Mail, mongoDate, Redirect, Response, Session, URL, View, Validator;
use mPDF;

class StrikeRatePointsService
{
    public static function calculateStrikeRatePoints($format, $runs, $balls)
    {
        $format= strtoupper($format);  

        // Ensure $runs and $balls are numeric and return 0 for non-numeric values
        if (!is_numeric($runs) || !is_numeric($balls)) {
            return ['strike_rate' => 0, 'strike_rate_points' => 0];
        }

        // Return 0 if no balls were faced to avoid division by zero
        if ($balls == 0) {
            return 0;
        }

        // Calculate the strike rate
        // $strikeRate = ($runs / $balls) * 100;
        $strikeRate = round(($runs / $balls) * 100, 2);

        // Define point mappings for each format using associative arrays
        $pointsMap = [
            'T20' => [
                ['min' => 0,    'max' => 49.99,  'points' => -6, 'minBalls' => 10],
                ['min' => 50,   'max' => 59.99,  'points' => -4, 'minBalls' => 10],
                ['min' => 60,   'max' => 69.99,  'points' => -2, 'minBalls' => 10],
                ['min' => 130,  'max' => 149.99, 'points' => 2,  'minBalls' => 10],
                ['min' => 150.1, 'max' => 169.99, 'points' => 4,  'minBalls' => 10],
                ['min' => 170,  'max' => PHP_INT_MAX, 'points' => 6, 'minBalls' => 10],
            ],
            'ODI' => [
                ['min' => 0,    'max' => 29.99,  'points' => -6, 'minBalls' => 20],
                ['min' => 30,   'max' => 39.99,  'points' => -4, 'minBalls' => 20],
                ['min' => 40,   'max' => 49.99,  'points' => -2, 'minBalls' => 20],
                ['min' => 100,  'max' => 120,    'points' => 2,  'minBalls' => 20],
                ['min' => 120.01, 'max' => 140,  'points' => 4,  'minBalls' => 20],
                ['min' => 140.01, 'max' => PHP_INT_MAX, 'points' => 6, 'minBalls' => 20],
            ],
            'ONE-DAY' => [
                ['min' => 0,    'max' => 29.99,  'points' => -6, 'minBalls' => 20],
                ['min' => 30,   'max' => 39.99,  'points' => -4, 'minBalls' => 20],
                ['min' => 40,   'max' => 49.99,  'points' => -2, 'minBalls' => 20],
                ['min' => 100,  'max' => 120,    'points' => 2,  'minBalls' => 20],
                ['min' => 120.01, 'max' => 140,  'points' => 4,  'minBalls' => 20],
                ['min' => 140.01, 'max' => PHP_INT_MAX, 'points' => 6, 'minBalls' => 20],
            ],
            'T10' => [
                ['min' => 0,    'max' => 59.99,  'points' => -6, 'minBalls' => 5],
                ['min' => 60,   'max' => 69.99,  'points' => -4, 'minBalls' => 5],
                ['min' => 70,   'max' => 79.99,  'points' => -2, 'minBalls' => 5],
                ['min' => 150.1, 'max' => 169.99, 'points' => 2,  'minBalls' => 5],
                ['min' => 170.1, 'max' => 189.99, 'points' => 4,  'minBalls' => 5],
                ['min' => 190,  'max' => PHP_INT_MAX, 'points' => 6, 'minBalls' => 5],
            ]
        ];

        // Check if the format exists in the points map
        if (!isset($pointsMap[$format])) {
            return ['strike_rate' => $strikeRate, 'strike_rate_points' => 0]; // Return strike rate and 0 points if format is unknown
        }

        // Loop through the defined ranges for the given format
        foreach ($pointsMap[$format] as $range) {
            if ($balls >= $range['minBalls'] && $strikeRate >= $range['min'] && $strikeRate <= $range['max']) {
                return ['strike_rate' => $strikeRate, 'strike_rate_points' => $range['points']]; // Return the points if the strike rate falls within the range
            }
        }

        // Default return 0 if no range matches
        return ['strike_rate' => $strikeRate, 'strike_rate_points' => 0];
    }
}
