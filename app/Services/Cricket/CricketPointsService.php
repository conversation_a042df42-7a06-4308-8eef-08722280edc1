<?php

namespace App\Services\Cricket;

class CricketPointsService
{
    protected static $pointsConfig = [
        'T20' => [
            'boundary' => 1,
            'six' => 2,
            'run_bonus_30' => 4,
            'half_century' => 8,
            'century' => 16,
            'maiden_over' => 12,
            'lbw_bowled' => 8,
            'three_wickets' => 4,
            'four_wickets' => 8,
            'five_wickets_or_more' => 16,
            'two_wickets' => 0, // not applicable
            'catch' => 8,
            'stumping' => 12,
            'run_out' => 6,
        ],
        'ONE-DAY' => [
            'boundary' => 1,
            'six' => 2,
            'run_bonus_30' => 0, // not applicable
            'half_century' => 4,
            'century' => 8,
            'maiden_over' => 4,
            'lbw_bowled' => 8,
            'three_wickets' => 0, // not applicable
            'four_wickets' => 4,
            'five_wickets_or_more' => 8,
            'two_wickets' => 0, // not applicable
            'catch' => 8,
            'stumping' => 12,
            'run_out' => 6,
        ],
        'ODI' => [
            'boundary' => 1,
            'six' => 2,
            'run_bonus_30' => 0, // not applicable
            'half_century' => 4,
            'century' => 8,
            'maiden_over' => 4,
            'lbw_bowled' => 8,
            'three_wickets' => 0, // not applicable
            'four_wickets' => 4,
            'five_wickets_or_more' => 8,
            'two_wickets' => 0, // not applicable
            'catch' => 8,
            'stumping' => 12,
            'run_out' => 6,
        ],
        'TEST' => [
            'boundary' => 1,
            'six' => 2,
            'run_bonus_30' => 0, // not applicable
            'half_century' => 4,
            'century' => 8,
            'maiden_over' => 0, // not applicable
            'lbw_bowled' => 8,
            'three_wickets' => 0, // not applicable
            'four_wickets' => 4,
            'five_wickets_or_more' => 8,
            'two_wickets' => 0, // not applicable
            'catch' => 8,
            'stumping' => 12,
            'run_out' => 6,
        ],
        'T10' => [
            'boundary' => 1,
            'six' => 2,
            'run_bonus_30' => 8,
            'half_century' => 16,
            'century' => 0, // not applicable
            'maiden_over' => 16,
            'lbw_bowled' => 8,
            'three_wickets' => 16,
            'four_wickets' => 0, // not applicable
            'five_wickets_or_more' => 0, // not applicable
            'two_wickets' => 8,
            'catch' => 8,
            'stumping' => 12,
            'run_out' => 6,
        ]
    ];

    // Function to calculate points for different scenarios
    public static function calculateCricketBonusPoints($format, $type, $value = null)
    {
        $format = strtoupper($format);

        // Ensure the format exists in the pointsConfig
        if (!isset(self::$pointsConfig[$format])) {
            throw new \Exception("Unknown format: $format");
        }

        $points = 0;

        switch ($type) {
            case 'boundary':
                // Calculate points based on the number of boundaries
                $points = self::$pointsConfig[$format]['boundary'] * $value;
                break;

            case 'six':
                // Calculate points based on the number of sixes
                $points = self::$pointsConfig[$format]['six'] * $value;
                break;

            case 'run_bonus_30':
                // Check if the player scored more than or equal to 30 runs
                if ($value >= 30 && isset(self::$pointsConfig[$format]['run_bonus_30'])) {
                    $points = self::$pointsConfig[$format]['run_bonus_30'];
                }
                break;

            case 'half_century':
                // Check if the player scored more than or equal to 50 runs
                if ($value >= 50) {
                    $points = self::$pointsConfig[$format]['half_century'];
                }
                break;

            case 'century':
                // Check if the player scored more than or equal to 100 runs
                if ($value >= 100 && isset(self::$pointsConfig[$format]['century'])) {
                    $points = self::$pointsConfig[$format]['century'];
                }
                break;

            case 'maiden_over':
                // Award points for a maiden over (no runs scored)
                $points = self::$pointsConfig[$format]['maiden_over']* $value;
                break;

            case 'wickets':
                if ($format == 'T20') {
                    if ($value >= 5 && isset(self::$pointsConfig[$format]['five_wickets_or_more'])) {
                        $points = self::$pointsConfig[$format]['five_wickets_or_more'];
                    } elseif ($value >= 4 && isset(self::$pointsConfig[$format]['four_wickets'])) {
                        $points = self::$pointsConfig[$format]['four_wickets'];
                    } elseif ($value >= 3 && isset(self::$pointsConfig[$format]['three_wickets'])) {
                        $points = self::$pointsConfig[$format]['three_wickets'];
                    }
                } elseif ($format == 'ODI') {
                    if ($value >= 5 && isset(self::$pointsConfig[$format]['five_wickets_or_more'])) {
                        $points = self::$pointsConfig[$format]['five_wickets_or_more'];
                    } elseif ($value >= 4 && isset(self::$pointsConfig[$format]['four_wickets'])) {
                        $points = self::$pointsConfig[$format]['four_wickets'];
                    }
                } elseif ($format == 'TEST') {
                    if ($value >= 5 && isset(self::$pointsConfig[$format]['five_wickets_or_more'])) {
                        $points = self::$pointsConfig[$format]['five_wickets_or_more'];
                    } elseif ($value >= 4 && isset(self::$pointsConfig[$format]['four_wickets'])) {
                        $points = self::$pointsConfig[$format]['four_wickets'];
                    }
                } elseif ($format == 'T10') {
                    if ($value >= 3 && isset(self::$pointsConfig[$format]['three_wickets'])) {
                        $points = self::$pointsConfig[$format]['three_wickets'];
                    } elseif ($value >= 2 && isset(self::$pointsConfig[$format]['two_wickets'])) {
                        $points = self::$pointsConfig[$format]['two_wickets'];
                    }
                }
                break;

            case 'lbw_bowled':
                // Add points for LBW or Bowled dismissals
                $points = self::$pointsConfig[$format]['lbw_bowled'] * $value;
                break;

            case 'catch':
            case 'catches':
                // Add points for catches
                $points = self::$pointsConfig[$format]['catch'] * $value;
                break;

            case 'stumping':
                // Add points for stumpings
                $points = self::$pointsConfig[$format]['stumping'] * $value;
                break;

            case 'run_out':
                // Add points for run outs
                $points = self::$pointsConfig[$format]['run_out'] * $value;
                break;

            default:
                $points = 0;
                // throw new \Exception("Unknown point type: $type");
        }

        return $points;
    }
}
