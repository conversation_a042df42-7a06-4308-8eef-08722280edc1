<?php

namespace App\Services;

class PointsConfig
{
    /**
     * Normal points table by match format.
     */
    public const NORMAL = [
        'T20'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -2,   // batter/wk/all-rounder
        ],
        'ODI'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -3,
        ],
        'TEST' => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 16,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -4,
        ],
        'T10'  => [
            'starting_xi' =>  4,
            'run'         =>  1,
            'wicket'      => 25,
            'catch'       =>  8,
            'run_out'     =>  6,
            'stumping'    => 12,
            'duck'        => -2,
        ],
    ];

    /* ----------  BONUS  POINTS  ---------- */
    public const BONUS = [
        'T20' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  4,
            'half_century'   =>  8,
            'century'        => 16,
            'maiden_over'    => 12,
            'lbw_bowled'     =>  8,
            'wickets_3'      =>  4,
            'wickets_4'      =>  8,
            'wickets_5'      => 16,
        ],
        'ODI' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  0,   // N/A
            'half_century'   =>  4,
            'century'        =>  8,
            'maiden_over'    =>  4,
            'lbw_bowled'     =>  8,
            'wickets_4'      =>  4,
            'wickets_5'      =>  8,
        ],
        'TEST' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  0,
            'half_century'   =>  4,
            'century'        =>  8,
            'maiden_over'    =>  0,
            'lbw_bowled'     =>  8,
            'wickets_4'      =>  4,
            'wickets_5'      =>  8,
        ],
        'T10' => [
            'boundary'       =>  1,
            'six'            =>  2,
            'run_bonus_30'   =>  8,
            'half_century'   => 16,
            'century'        =>  0,
            'maiden_over'    => 16,
            'lbw_bowled'     =>  8,
            'wickets_2'      =>  8,
            'wickets_3'      => 16,
        ],
    ];

    /* ----------  ECONOMY-RATE  ---------- */
    public const ECONOMY = [
        'T20' => [
            'min_overs' => 2,
            'rules' => [
                [0,     4.99,   6],
                [5.0,   5.99,   4],
                [6.0,   7.00,   2],
                [10.0, 10.99,  -2],
                [11.0, 11.99,  -4],
                [12.0,  null,  -6],   // 12+
            ],
        ],
        'ODI' => [
            'min_overs' => 5,
            'rules' => [
                [0,    2.49,   6],
                [2.5,  3.49,   4],
                [3.5,  4.5,    2],
                [7.0,  7.99,  -2],
                [8.0,  8.99,  -4],
                [9.0,  null,  -6],
            ],
        ],
        'T10' => [
            'min_overs' => 1,
            'rules' => [
                [0,    6.99,   6],
                [7.0,  7.99,   4],
                [8.0,  8.99,   2],
                [14.0, 14.99, -2],
                [15.1, 15.99, -4],
                [16.0, null,  -6],
            ],
        ],
    ];

    /* ----------  STRIKE-RATE  ---------- */
    public const STRIKE_RATE = [
        'T20' => [
            'min_balls' => 10,
            'rules' => [
                [0,    49.99, -6],
                [50.0, 59.99, -4],
                [60.0, 70.0,  -2],
                [130.0, 149.99,  2],
                [150.0, 170.0,   4],
                [170.01, null,   6],
            ],
        ],
        'ODI' => [
            'min_balls' => 20,
            'rules' => [
                [0,   29.99, -6],
                [30.0, 39.99, -4],
                [40.0, 50.0,  -2],
                [100.0, 119.99, 2],
                [120.01, 140.0, 4],
                [140.01, null,  6],
            ],
        ],
        'T10' => [
            'min_balls' => 5,
            'rules' => [
                [0,   59.99, -6],
                [60.0, 69.99, -4],
                [70.0, 80.0,  -2],
                [150.1, 170.0, 2],
                [170.1, 190.0, 4],
                [190.01, null, 6],
            ],
        ],
    ];
}
