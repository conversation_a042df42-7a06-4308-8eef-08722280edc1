<?php

echo "🚀 Testing Fast Match Points API Performance...\n\n";

$baseUrl = 'http://localhost:8000/api/v1/match-points';
$token = 'ea9fc1ae0ce14b1394f7589bb2437878';
$gameId = '264117'; // Example game ID

$tests = [
    'Points Only' => ['type' => 'points'],
    'Counts Only' => ['type' => 'count'], 
    'All Data' => ['type' => 'all'],
];

foreach ($tests as $testName => $params) {
    echo "Testing: {$testName}\n";
    echo str_repeat('-', 40) . "\n";
    
    $url = $baseUrl . '/calculate?' . http_build_query(array_merge([
        'game_id' => $gameId,
        'token' => $token
    ], $params));
    
    $startTime = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    $endTime = microtime(true);
    $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    echo "HTTP Code: {$httpCode}\n";
    echo "Response Time: " . number_format($responseTime, 2) . "ms\n";
    
    if ($httpCode === 200) {
        $data = json_decode($body, true);
        if ($data) {
            echo "Status: {$data['status']}\n";
            echo "Response Type: {$data['response_type']}\n";
            echo "Total Players: {$data['total_players']}\n";
            echo "Data Size: " . number_format(strlen($body)) . " bytes\n";
            
            // Check cache headers
            if (preg_match('/Cache-Control: (.+)/i', $headers, $matches)) {
                echo "Cache Control: {$matches[1]}\n";
            }
            
            if (preg_match('/X-Response-Type: (.+)/i', $headers, $matches)) {
                echo "Response Type Header: {$matches[1]}\n";
            }
        }
    } else {
        echo "Error: {$body}\n";
    }
    
    echo "\n";
}

// Test formats endpoint
echo "Testing Formats Endpoint\n";
echo str_repeat('-', 40) . "\n";

$url = $baseUrl . '/formats';
$startTime = microtime(true);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$endTime = microtime(true);
$responseTime = ($endTime - $startTime) * 1000;

echo "HTTP Code: {$httpCode}\n";
echo "Response Time: " . number_format($responseTime, 2) . "ms\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data) {
        echo "Available Formats: " . implode(', ', $data['available_formats']) . "\n";
        echo "Response Types: " . implode(', ', $data['response_types']) . "\n";
        echo "Cache TTL: {$data['cache_ttl']} seconds\n";
    }
}

echo "\n✅ Performance test completed!\n";
