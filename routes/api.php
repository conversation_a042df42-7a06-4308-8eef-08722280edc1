<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\FastMatchPointsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Fast Match Points API Routes  
|--------------------------------------------------------------------------
| High-performance API endpoints for cricket match points calculation
| with conditional response filtering
*/

// http://127.0.0.1:8000/api/v1/match-points/calculate?game_id=264117&type=count&token=ea9fc1ae0ce14b1394f7589bb2437878
// http://127.0.0.1:8000/api/v1/match-points/calculate?game_id=264117&type=points&token=ea9fc1ae0ce14b1394f7589bb2437878
Route::prefix('v1/match-points')->group(function () {
    // Main endpoint for match points calculation
    Route::get('/calculate', [FastMatchPointsController::class, 'calculate'])
        ->name('api.match-points.calculate');
    
    // Get available formats and response types
    Route::get('/formats', [FastMatchPointsController::class, 'formats'])
        ->name('api.match-points.formats');
    
    // Clear cache for specific match
    Route::delete('/cache', [FastMatchPointsController::class, 'clearCache'])
        ->name('api.match-points.clear-cache');
});


