<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\SportsInteractiveMatchPointsController;
use App\Http\Controllers\Admin\FastSportsInteractiveController;
use App\Http\Controllers\Admin\SportsInteractiveMatchPointsNewController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Example API Calling : http://127.0.0.1:8000/get-sport-interactive-scorecard?game_id=263023&token=ea9fc1ae0ce14b1394f7589bb2437878&response=

// For show PlayerWise Point 
Route::get('/get-sport-interactive-scorecard', [SportsInteractiveMatchPointsController::class, 'calculate']);

// for PlayerScore Card 
Route::get('/get-sport-interactive-player-scorecard', [FastSportsInteractiveController::class, 'calculate']);

Route::get('/new-get-sport-interactive-scorecard', [SportsInteractiveMatchPointsNewController::class, 'calculate']);

// Fast Match Points Tester View
Route::get('/admin/fast-match-points', function () {
    return view('admin.fast-match-points');
})->name('admin.fast-match-points');
